#!/usr/bin/env python3
"""
语义优化的中文搜索器
基于英文词汇池和语义匹配的中文搜索优化
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional, Set
from pathlib import Path
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from difflib import SequenceMatcher
from fixed_searcher import FixedFieldSearcher, SearchResult
import warnings
warnings.filterwarnings('ignore')

class SemanticChineseSearcher(FixedFieldSearcher):
    """语义优化的中文搜索器"""
    
    def __init__(self, data_dir: str = "split_files"):
        # 先调用父类初始化
        super().__init__(data_dir)
        
        # 构建英文词汇池
        self._build_english_vocabulary_pool()
        
        # 构建中英文语义映射
        self._build_semantic_mapping()
        
        print(f"英文词汇池构建完成，共 {len(self.english_vocab_pool)} 个词汇")
    
    def _build_english_vocabulary_pool(self):
        """构建英文词汇池"""
        print("正在构建英文词汇池...")
        
        self.english_vocab_pool = set()
        
        # 从所有描述中提取英文词汇
        for desc in self.descriptions:
            # 提取英文单词（字母组成的词，长度>=2）
            english_words = re.findall(r'\b[a-zA-Z]{2,}\b', desc.lower())
            self.english_vocab_pool.update(english_words)
        
        # 过滤掉常见停用词和无意义词汇
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'must', 'can', 'shall', 'it', 'its', 'an', 'as'
        }
        
        # 过滤停用词和长度小于3的词
        self.english_vocab_pool = {
            word for word in self.english_vocab_pool 
            if word not in stop_words and len(word) >= 3
        }
        
        # 转换为列表以便索引
        self.english_vocab_list = sorted(list(self.english_vocab_pool))
        
        # 为英文词汇池构建TF-IDF向量
        self._build_vocab_vectors()
    
    def _build_vocab_vectors(self):
        """为英文词汇池构建向量表示"""
        print("正在为词汇池构建向量表示...")
        
        # 使用字符级n-gram来表示词汇，捕捉词汇的字符特征
        self.vocab_vectorizer = TfidfVectorizer(
            analyzer='char',
            ngram_range=(2, 4),
            max_features=1000,
            lowercase=True
        )
        
        # 为每个英文词汇构建向量
        self.vocab_vectors = self.vocab_vectorizer.fit_transform(self.english_vocab_list)
    
    def _build_semantic_mapping(self):
        """构建中英文语义映射表"""
        print("正在构建中英文语义映射...")
        
        # 基础语义映射（人工定义的高质量映射）
        self.base_semantic_mapping = {
            # 金融基础概念
            '价格': ['price', 'cost', 'value', 'pricing'],
            '市值': ['market', 'cap', 'capitalization', 'mktcap'],
            '成交量': ['volume', 'trading', 'turnover'],
            '现金流': ['cash', 'flow', 'cashflow'],
            '收入': ['revenue', 'income', 'earnings'],
            '利润': ['profit', 'earnings', 'income', 'gain'],
            '资产': ['asset', 'assets', 'property'],
            '负债': ['liability', 'debt', 'obligation'],
            '股价': ['stock', 'share', 'equity', 'price'],
            '股息': ['dividend', 'yield', 'payout'],
            '收益': ['earnings', 'return', 'yield', 'profit'],
            '增长': ['growth', 'increase', 'expansion'],
            '风险': ['risk', 'volatility', 'beta'],
            '回报': ['return', 'yield', 'performance'],
            '波动': ['volatility', 'variance', 'fluctuation'],
            '比率': ['ratio', 'rate', 'percentage'],
            '指标': ['indicator', 'metric', 'measure'],
            '财务': ['financial', 'finance', 'fiscal'],
            '会计': ['accounting', 'bookkeeping'],
            '估值': ['valuation', 'value', 'worth'],
            '分析': ['analysis', 'analytics', 'evaluation'],
            '预测': ['forecast', 'prediction', 'estimate'],
            '趋势': ['trend', 'direction', 'movement'],
            '表现': ['performance', 'result', 'outcome'],
            '基金': ['fund', 'mutual', 'investment'],
            '债券': ['bond', 'debenture', 'security'],
            '期权': ['option', 'derivative', 'warrant'],
            '期货': ['future', 'forward', 'contract'],
            '汇率': ['exchange', 'currency', 'forex'],
            '通胀': ['inflation', 'cpi', 'deflation'],
            '利率': ['interest', 'rate', 'yield'],
            '银行': ['bank', 'banking', 'financial'],
            '保险': ['insurance', 'coverage', 'policy'],
            '投资': ['investment', 'invest', 'portfolio'],
            '交易': ['trading', 'transaction', 'deal'],
            '市场': ['market', 'marketplace', 'exchange'],
            '行业': ['industry', 'sector', 'business'],
            '公司': ['company', 'corporation', 'firm'],
            '股东': ['shareholder', 'stockholder', 'equity'],
            '管理': ['management', 'administration', 'governance'],
            '策略': ['strategy', 'strategic', 'plan'],
            '竞争': ['competition', 'competitive', 'rival'],
            '创新': ['innovation', 'innovative', 'development'],
            '技术': ['technology', 'technical', 'tech'],
            '数据': ['data', 'information', 'statistics'],
            '报告': ['report', 'reporting', 'statement'],
            '季度': ['quarter', 'quarterly', 'period'],
            '年度': ['annual', 'yearly', 'year'],
            '月度': ['monthly', 'month', 'period'],
            '日度': ['daily', 'day', 'date'],
            '历史': ['historical', 'history', 'past'],
            '当前': ['current', 'present', 'latest'],
            '未来': ['future', 'forward', 'projected']
        }
    
    def _find_semantic_matches(self, chinese_word: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """为中文词找到语义最相近的英文词"""
        # 首先检查基础映射
        if chinese_word in self.base_semantic_mapping:
            base_matches = self.base_semantic_mapping[chinese_word]
            # 验证这些词是否在词汇池中
            valid_matches = [(word, 1.0) for word in base_matches if word in self.english_vocab_pool]
            if valid_matches:
                return valid_matches[:top_k]
        
        # 如果基础映射没有，使用字符相似度匹配
        chinese_vector = self.vocab_vectorizer.transform([chinese_word])
        similarities = cosine_similarity(chinese_vector, self.vocab_vectors).flatten()
        
        # 获取最相似的词汇
        top_indices = np.argsort(similarities)[::-1][:top_k * 2]  # 取更多候选
        
        matches = []
        for idx in top_indices:
            if similarities[idx] > 0.1:  # 最小相似度阈值
                english_word = self.english_vocab_list[idx]
                # 额外的语义过滤
                if self._is_semantically_relevant(chinese_word, english_word):
                    matches.append((english_word, similarities[idx]))
        
        return matches[:top_k]
    
    def _is_semantically_relevant(self, chinese_word: str, english_word: str) -> bool:
        """判断中英文词汇是否语义相关"""
        # 简单的语义相关性检查
        chinese_chars = set(chinese_word)
        english_chars = set(english_word.lower())
        
        # 如果有共同字符（如数字），可能相关
        if chinese_chars.intersection(english_chars):
            return True
        
        # 长度相似性检查
        if abs(len(chinese_word) - len(english_word)) <= 2:
            return True
        
        # 词汇长度合理性检查
        if 3 <= len(english_word) <= 15:
            return True
        
        return False
    
    def _expand_chinese_query_semantic(self, query: str) -> str:
        """使用语义匹配扩展中文查询"""
        # 中文分词
        chinese_words = list(jieba.cut(query))
        chinese_words = [word.strip() for word in chinese_words if len(word.strip()) > 1]
        
        expanded_terms = []
        
        for chinese_word in chinese_words:
            # 如果是英文词，直接保留
            if re.match(r'^[a-zA-Z]+$', chinese_word):
                expanded_terms.append(chinese_word)
                continue
            
            # 如果是中文词，寻找语义匹配
            if re.search(r'[\u4e00-\u9fff]', chinese_word):
                semantic_matches = self._find_semantic_matches(chinese_word, top_k=3)
                
                if semantic_matches:
                    # 添加匹配的英文词
                    matched_words = [match[0] for match in semantic_matches]
                    expanded_terms.extend(matched_words)
                    
                    print(f"中文词 '{chinese_word}' 映射到: {matched_words}")
                else:
                    # 如果没有找到语义匹配，保留原词
                    expanded_terms.append(chinese_word)
        
        # 构建扩展查询
        if expanded_terms:
            expanded_query = query + ' ' + ' '.join(expanded_terms)
            return expanded_query
        
        return query
    
    def _calculate_enhanced_similarity(self, query: str) -> np.ndarray:
        """计算增强的相似度（优化中文搜索）"""
        # 检查是否包含中文
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', query))
        
        if has_chinese:
            # 使用语义扩展
            expanded_query = self._expand_chinese_query_semantic(query)
            print(f"原查询: '{query}' -> 扩展查询: '{expanded_query}'")
        else:
            expanded_query = query
        
        # 使用父类的方法计算相似度
        simple_sim = self._calculate_simple_similarity(expanded_query)
        cosine_sim = self._calculate_enhanced_cosine_similarity(expanded_query)
        
        # 如果是中文查询，给予语义匹配更高权重
        if has_chinese:
            similarities = 0.8 * simple_sim + 0.2 * cosine_sim
        else:
            similarities = 0.7 * simple_sim + 0.3 * cosine_sim
        
        return similarities
    
    def search(self, 
               query: str, 
               top_k: int = 10,
               similarity_method: str = 'semantic_enhanced',
               min_score: float = 0.01,
               region_filter: Optional[str] = None,
               type_filter: Optional[str] = None,
               table_filter: Optional[str] = None,
               category_filter: Optional[str] = None) -> List[SearchResult]:
        """
        执行语义优化搜索
        """
        if not query.strip():
            return []
        
        print(f"语义搜索查询: '{query}' (方法: {similarity_method})")
        
        # 计算相似度
        if similarity_method == 'semantic_enhanced':
            similarities = self._calculate_enhanced_similarity(query)
        else:
            # 使用父类的方法
            return super().search(query, top_k, similarity_method, min_score, 
                                region_filter, type_filter, table_filter, category_filter)
        
        # 应用过滤器
        mask = similarities >= min_score
        if region_filter:
            mask &= (self.data['region'] == region_filter)
        if type_filter:
            mask &= (self.data['type'] == type_filter)
        if table_filter:
            mask &= (self.data['table_name'] == table_filter)
        if category_filter:
            mask &= (self.data['category.name'] == category_filter)
        
        # 获取有效索引
        valid_indices = np.where(mask)[0]
        valid_similarities = similarities[mask]
        
        if len(valid_similarities) == 0:
            print("找到 0 个匹配结果")
            return []
        
        # 排序并获取top_k结果
        sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
        
        # 构建结果
        results = []
        for idx in sorted_indices:
            original_idx = valid_indices[idx]
            row = self.data.iloc[original_idx]
            
            result = SearchResult(
                id=row['id'],
                description=row['description'],
                similarity_score=valid_similarities[idx],
                region=row.get('region', ''),
                universe=row.get('universe', ''),
                type=row.get('type', ''),
                category=row.get('category.name', ''),
                subcategory=row.get('subcategory.name', ''),
                dataset_name=row.get('dataset.name', ''),
                coverage=row.get('coverage', 0.0),
                user_count=row.get('userCount', 0),
                alpha_count=row.get('alphaCount', 0)
            )
            result.table_name = row.get('table_name', '')
            results.append(result)
        
        print(f"找到 {len(results)} 个匹配结果")
        return results
    
    def analyze_vocabulary_coverage(self) -> Dict:
        """分析词汇池覆盖情况"""
        stats = {
            'total_english_words': len(self.english_vocab_pool),
            'base_mapping_coverage': len(self.base_semantic_mapping),
            'top_frequent_words': {},
            'word_length_distribution': {}
        }
        
        # 统计词汇长度分布
        length_dist = {}
        for word in self.english_vocab_pool:
            length = len(word)
            length_dist[length] = length_dist.get(length, 0) + 1
        
        stats['word_length_distribution'] = dict(sorted(length_dist.items()))
        
        return stats


def main():
    """测试语义优化搜索器"""
    print("测试语义优化的中文搜索器")
    print("="*60)
    
    try:
        searcher = SemanticChineseSearcher()
        
        # 分析词汇池
        vocab_stats = searcher.analyze_vocabulary_coverage()
        print(f"\n词汇池统计:")
        print(f"- 英文词汇总数: {vocab_stats['total_english_words']}")
        print(f"- 基础映射覆盖: {vocab_stats['base_mapping_coverage']} 个中文词")
        print(f"- 词汇长度分布: {vocab_stats['word_length_distribution']}")
        
        # 测试中文搜索
        chinese_queries = [
            "市值",
            "价格波动", 
            "现金流分析",
            "收益率",
            "风险指标",
            "股息收入",
            "财务报表"
        ]
        
        print(f"\n语义优化中文搜索测试:")
        print("-" * 40)
        
        for query in chinese_queries:
            print(f"\n查询: '{query}'")
            results = searcher.search(query, top_k=3, similarity_method='semantic_enhanced')
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. [{result.similarity_score:.4f}] {result.id}: {result.description[:50]}...")
            else:
                print("  未找到匹配结果")
        
        print(f"\n语义优化搜索器测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
