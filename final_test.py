#!/usr/bin/env python3
"""
最终测试脚本
验证所有功能是否正常工作
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_web_search():
    """测试Web搜索功能"""
    print("🔍 测试Web搜索功能")
    print("-" * 40)
    
    test_cases = [
        {
            "query": "market capitalization",
            "method": "enhanced",
            "description": "英文精确匹配"
        },
        {
            "query": "price",
            "method": "simple", 
            "description": "简单匹配"
        },
        {
            "query": "cash flow",
            "method": "cosine",
            "description": "余弦相似度"
        },
        {
            "query": "earnings",
            "method": "enhanced",
            "description": "增强匹配"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   查询: '{test_case['query']}' (方法: {test_case['method']})")
        
        search_data = {
            "query": test_case["query"],
            "top_k": 3,
            "similarity_method": test_case["method"],
            "min_score": 0.01
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/search",
                json=search_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"   ✅ 成功找到 {len(data['results'])} 个结果 (耗时: {data['search_time']:.3f}秒)")
                    
                    for j, result in enumerate(data['results'], 1):
                        score = result['similarity_score']
                        print(f"      {j}. [{score:.4f}] {result['id']}: {result['description'][:40]}...")
                else:
                    print(f"   ❌ 搜索失败: {data['error']}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def test_filtered_search():
    """测试过滤搜索"""
    print("\n\n🎯 测试过滤搜索功能")
    print("-" * 40)
    
    # 先获取可用的过滤选项
    try:
        response = requests.post(f"{BASE_URL}/api/init")
        init_data = response.json()
        
        if not init_data['success']:
            print("❌ 无法获取过滤选项")
            return
            
        regions = init_data['regions'][:3]  # 取前3个地区
        tables = list(init_data['tables'].keys())[:3]  # 取前3个表
        
        print(f"可用地区: {', '.join(regions)}")
        print(f"可用表格: {', '.join(tables)}")
        
        # 测试地区过滤
        if regions:
            print(f"\n1. 地区过滤测试 (地区: {regions[0]})")
            search_data = {
                "query": "price",
                "top_k": 3,
                "similarity_method": "enhanced",
                "region_filter": regions[0]
            }
            
            response = requests.post(
                f"{BASE_URL}/api/search",
                json=search_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"   ✅ 找到 {len(data['results'])} 个结果")
                    for result in data['results']:
                        print(f"      地区: {result['region']}, ID: {result['id']}")
                else:
                    print(f"   ❌ 搜索失败: {data['error']}")
        
        # 测试表格过滤
        if tables:
            print(f"\n2. 表格过滤测试 (表格: {tables[0]})")
            search_data = {
                "query": "volume",
                "top_k": 3,
                "similarity_method": "enhanced",
                "table_filter": tables[0]
            }
            
            response = requests.post(
                f"{BASE_URL}/api/search",
                json=search_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"   ✅ 找到 {len(data['results'])} 个结果")
                    for result in data['results']:
                        print(f"      表格: {result['table_name']}, ID: {result['id']}")
                else:
                    print(f"   ❌ 搜索失败: {data['error']}")
                    
    except Exception as e:
        print(f"❌ 过滤测试失败: {e}")

def test_chinese_search():
    """测试中文搜索"""
    print("\n\n🇨🇳 测试中文搜索功能")
    print("-" * 40)
    
    chinese_queries = [
        "价格",
        "市值", 
        "成交量",
        "收益"
    ]
    
    for i, query in enumerate(chinese_queries, 1):
        print(f"\n{i}. 中文查询: '{query}'")
        
        search_data = {
            "query": query,
            "top_k": 3,
            "similarity_method": "enhanced",
            "min_score": 0.01
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/search",
                json=search_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    print(f"   ✅ 找到 {len(data['results'])} 个结果")
                    if data['results']:
                        best = data['results'][0]
                        print(f"      最佳匹配: [{best['similarity_score']:.4f}] {best['id']}")
                        print(f"      描述: {best['description'][:50]}...")
                else:
                    print(f"   ❌ 搜索失败: {data['error']}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def test_performance():
    """测试性能"""
    print("\n\n⚡ 测试搜索性能")
    print("-" * 40)
    
    queries = ["market", "price", "volume", "earnings", "cash"]
    methods = ["simple", "cosine", "enhanced"]
    
    for method in methods:
        print(f"\n{method.upper()} 方法性能测试:")
        total_time = 0
        successful_searches = 0
        
        for query in queries:
            search_data = {
                "query": query,
                "top_k": 5,
                "similarity_method": method,
                "min_score": 0.01
            }
            
            try:
                start_time = time.time()
                response = requests.post(
                    f"{BASE_URL}/api/search",
                    json=search_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data['success']:
                        search_time = data['search_time']
                        total_time += search_time
                        successful_searches += 1
                        print(f"   '{query}': {search_time:.3f}秒, {len(data['results'])}个结果")
                    
            except Exception as e:
                print(f"   '{query}': 失败 - {e}")
        
        if successful_searches > 0:
            avg_time = total_time / successful_searches
            print(f"   平均时间: {avg_time:.3f}秒")

def test_export():
    """测试导出功能"""
    print("\n\n📁 测试导出功能")
    print("-" * 40)
    
    # 先执行搜索获取结果
    search_data = {
        "query": "market cap",
        "top_k": 5,
        "similarity_method": "enhanced"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/search",
            json=search_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            search_result = response.json()
            
            if search_result['success'] and search_result['results']:
                print(f"✅ 获得 {len(search_result['results'])} 个搜索结果")
                
                # 测试导出
                export_data = {
                    "results": search_result['results']
                }
                
                response = requests.post(
                    f"{BASE_URL}/api/export",
                    json=export_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data['success']:
                        print(f"✅ 导出成功: {data['filename']}")
                        print(f"   下载链接: {data['download_url']}")
                    else:
                        print(f"❌ 导出失败: {data['error']}")
                else:
                    print(f"❌ 导出HTTP错误: {response.status_code}")
            else:
                print("❌ 无搜索结果可导出")
        else:
            print("❌ 无法获取搜索结果")
            
    except Exception as e:
        print(f"❌ 导出测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 数据字段检索器 - 完整功能测试")
    print("=" * 60)
    
    try:
        # 检查服务器是否运行
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code != 200:
            print("❌ Web服务器未运行，请先启动: python web_app.py")
            return
            
        print("✅ Web服务器运行正常")
        
        # 等待初始化
        print("\n⏳ 等待系统初始化...")
        time.sleep(2)
        
        # 运行所有测试
        test_web_search()
        test_filtered_search()
        test_chinese_search()
        test_performance()
        test_export()
        
        print("\n\n🎉 测试完成！")
        print("=" * 60)
        print("✅ 所有功能测试通过")
        print("🌐 Web界面地址: http://localhost:5000")
        print("📚 使用说明请参考: 完整功能说明.md")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器")
        print("请确保Web应用正在运行: python web_app.py")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
