#!/usr/bin/env python3
"""
预翻译脚本 - 批量翻译所有描述并保存到缓存
"""

import pandas as pd
import json
import time
from pathlib import Path
from deep_translator import GoogleTranslator
import os

def load_all_descriptions():
    """加载所有唯一的描述"""
    print("🔍 加载所有数据文件...")
    
    all_descriptions = set()
    data_dir = Path("split_files")
    
    for csv_file in data_dir.glob("*.csv"):
        try:
            df = pd.read_csv(csv_file)
            if 'description' in df.columns:
                descriptions = df['description'].dropna().unique()
                all_descriptions.update(descriptions)
                print(f"   已处理: {csv_file.name} ({len(descriptions)} 条描述)")
        except Exception as e:
            print(f"   ❌ 处理失败 {csv_file.name}: {e}")
    
    print(f"✅ 总共找到 {len(all_descriptions)} 条唯一描述")
    return list(all_descriptions)

def translate_descriptions(descriptions, batch_size=50, delay=0.1):
    """批量翻译描述"""
    print(f"🌐 开始翻译 {len(descriptions)} 条描述...")
    
    # 初始化翻译器
    try:
        translator = GoogleTranslator(source='en', target='zh-CN')
        print("✅ 翻译器初始化成功")
    except Exception as e:
        print(f"❌ 翻译器初始化失败: {e}")
        return {}
    
    # 加载现有翻译缓存
    cache_file = Path("cache/chinese_translations.json")
    cache_file.parent.mkdir(exist_ok=True)
    
    translations = {}
    if cache_file.exists():
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
            print(f"📁 已加载 {len(translations)} 条缓存翻译")
        except Exception as e:
            print(f"⚠️ 加载缓存失败: {e}")
    
    # 过滤出需要翻译的描述
    to_translate = [desc for desc in descriptions if desc not in translations]
    print(f"📝 需要翻译 {len(to_translate)} 条新描述")
    
    if not to_translate:
        print("✅ 所有描述都已翻译完成")
        return translations
    
    # 批量翻译
    translated_count = 0
    total = len(to_translate)
    
    for i in range(0, total, batch_size):
        batch = to_translate[i:i + batch_size]
        
        print(f"\n📦 处理批次 {i//batch_size + 1}/{(total-1)//batch_size + 1}")
        
        for desc in batch:
            if not desc or not desc.strip():
                continue
            
            try:
                # 翻译
                chinese_text = translator.translate(desc)
                translations[desc] = chinese_text
                translated_count += 1
                
                # 显示进度
                if translated_count % 10 == 0:
                    progress = (i + len([d for d in batch[:batch.index(desc)+1] if d])) / total * 100
                    print(f"   进度: {translated_count}/{len(to_translate)} ({progress:.1f}%)")
                
                # 避免请求过于频繁
                time.sleep(delay)
                
            except Exception as e:
                print(f"   ❌ 翻译失败 '{desc[:30]}...': {e}")
                # 如果翻译失败，使用原文
                translations[desc] = desc
        
        # 每批次后保存缓存
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(translations, f, ensure_ascii=False, indent=2)
            print(f"   💾 已保存 {len(translations)} 条翻译到缓存")
        except Exception as e:
            print(f"   ⚠️ 保存缓存失败: {e}")
    
    print(f"\n🎉 翻译完成！")
    print(f"   成功翻译: {translated_count} 条")
    print(f"   总翻译数: {len(translations)} 条")
    
    return translations

def show_translation_samples(translations, num_samples=10):
    """显示翻译样例"""
    print(f"\n📋 翻译样例 (前{num_samples}条):")
    print("-" * 80)
    
    count = 0
    for english, chinese in translations.items():
        if count >= num_samples:
            break
        if chinese != english:  # 只显示实际翻译的内容
            print(f"{count+1:2d}. {english[:40]:<40} -> {chinese}")
            count += 1
    
    if count == 0:
        print("   暂无翻译样例")

def main():
    """主函数"""
    print("🚀 数据字段描述预翻译工具")
    print("=" * 50)
    
    try:
        # 1. 加载所有描述
        descriptions = load_all_descriptions()
        
        if not descriptions:
            print("❌ 未找到任何描述，请检查数据文件")
            return
        
        # 2. 翻译描述
        translations = translate_descriptions(descriptions)
        
        # 3. 显示样例
        show_translation_samples(translations)
        
        # 4. 统计信息
        print(f"\n📊 统计信息:")
        print(f"   总描述数: {len(descriptions):,}")
        print(f"   翻译数量: {len(translations):,}")
        print(f"   实际翻译: {len([t for t in translations.values() if t != list(translations.keys())[list(translations.values()).index(t)]]):,}")
        print(f"   缓存文件: cache/chinese_translations.json")
        
        print(f"\n💡 使用说明:")
        print("   翻译缓存已保存，下次启动Web应用时会自动加载")
        print("   如需启用翻译功能，请修改搜索器初始化参数：")
        print("   FixedFieldSearcher(enable_translation=True)")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断翻译过程")
    except Exception as e:
        print(f"\n❌ 翻译过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
