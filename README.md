# 数据字段检索器

一个基于多种文本相似度算法的高效数据字段搜索系统，专门用于搜索和匹配金融数据字段描述。

## 功能特点

### 🔍 多种相似度算法
- **余弦相似度 (Cosine)**: 基于TF-IDF向量的语义相似度
- **模糊匹配 (Fuzzy)**: 基于编辑距离的字符串相似度
- **关键词匹配 (Keyword)**: 基于Jaccard相似度的关键词匹配
- **混合方法 (Hybrid)**: 综合多种算法的加权组合 (推荐)

### 🌐 中英文混合支持
- 智能中英文文本预处理
- 中文分词 (jieba)
- 英文词干提取和标准化

### 🎯 高级过滤功能
- 按地区过滤 (CHN, USA, EUR, etc.)
- 按数据类型过滤 (MATRIX, VECTOR, GROUP)
- 按分类过滤
- 相似度阈值设置

### ⚡ 高性能搜索
- 预构建TF-IDF索引
- 向量化计算
- 批量处理优化

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 基本使用

```python
from field_searcher import FieldSearcher

# 初始化搜索器
searcher = FieldSearcher("split_files")

# 执行搜索
results = searcher.search("market capitalization", top_k=10)

# 显示结果
searcher.print_results(results)
```

### 2. 交互式搜索

```bash
python interactive_search.py
```

交互式命令:
- `search <查询词>` - 执行搜索
- `method <方法名>` - 设置相似度方法
- `top <数量>` - 设置返回结果数量
- `filter region <地区>` - 按地区过滤
- `show <索引>` - 显示详细结果
- `export <文件名>` - 导出结果到CSV

### 3. 命令行搜索

```bash
# 直接搜索
python interactive_search.py --query "market cap" --method hybrid --top 5

# 指定数据目录
python interactive_search.py --data-dir /path/to/data --query "price"
```

### 4. 演示脚本

```bash
python demo.py
```

## 相似度方法详解

### Cosine Similarity (余弦相似度)
- **原理**: 基于TF-IDF向量计算文档间的余弦相似度
- **优势**: 能够捕捉语义相似性，对文档长度不敏感
- **适用**: 语义搜索，概念匹配

### Fuzzy Matching (模糊匹配)
- **原理**: 使用编辑距离计算字符串相似度
- **优势**: 能够处理拼写错误和变体
- **适用**: 精确字符串匹配，处理输入错误

### Keyword Matching (关键词匹配)
- **原理**: 基于Jaccard相似度计算关键词集合重叠度
- **优势**: 直观的关键词匹配，计算快速
- **适用**: 关键词搜索，标签匹配

### Hybrid Method (混合方法) - 推荐
- **原理**: 加权组合多种算法 (50% Cosine + 30% Fuzzy + 20% Keyword)
- **优势**: 综合各算法优点，搜索效果最佳
- **适用**: 通用搜索场景

## 高级用法

### 自定义相似度权重

```python
# 在field_searcher.py中修改混合方法权重
similarities = (0.6 * cosine_sim + 0.2 * fuzzy_sim + 0.2 * keyword_sim)
```

### 批量搜索

```python
queries = ["market cap", "price", "volume"]
all_results = []

for query in queries:
    results = searcher.search(query, top_k=5)
    all_results.extend(results)
```

### 结果导出

```python
import pandas as pd

# 转换结果为DataFrame
data = [{
    'ID': r.id,
    'Description': r.description,
    'Score': r.similarity_score,
    'Region': r.region
} for r in results]

df = pd.DataFrame(data)
df.to_csv('search_results.csv', index=False)
```

## 数据格式

输入CSV文件应包含以下列:
- `id`: 字段标识符
- `description`: 字段描述 (主要搜索目标)
- `region`: 地区
- `type`: 数据类型
- `category.name`: 分类名称
- `subcategory.name`: 子分类名称
- 其他元数据列...

## 性能优化建议

1. **预处理优化**: 对于大型数据集，考虑预计算TF-IDF矩阵
2. **内存管理**: 使用分批处理处理超大数据集
3. **索引优化**: 为频繁查询的字段建立专门索引
4. **缓存策略**: 缓存常用查询结果

## 故障排除

### 常见问题

1. **中文分词问题**
   ```python
   import jieba
   jieba.initialize()  # 确保jieba正确初始化
   ```

2. **内存不足**
   ```python
   # 减少max_features参数
   TfidfVectorizer(max_features=5000)
   ```

3. **搜索结果为空**
   - 检查min_score阈值是否过高
   - 确认数据文件格式正确
   - 验证查询词是否有效

## 扩展功能

### 添加新的相似度算法

```python
def _calculate_custom_similarity(self, query: str) -> np.ndarray:
    """自定义相似度算法"""
    # 实现您的算法
    pass

# 在search方法中添加新选项
elif similarity_method == 'custom':
    similarities = self._calculate_custom_similarity(query)
```

### 集成外部API

```python
# 例如：集成词向量API
def _calculate_word2vec_similarity(self, query: str) -> np.ndarray:
    # 调用外部词向量服务
    pass
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
