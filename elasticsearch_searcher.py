#!/usr/bin/env python3
"""
Elasticsearch集成搜索器
使用Elasticsearch进行高效的中英文混合搜索
"""

import pandas as pd
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Elasticsearch相关导入
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk

@dataclass
class ESSearchResult:
    """Elasticsearch搜索结果"""
    id: str
    description: str
    description_zh: str
    similarity_score: float
    region: str
    universe: str
    type: str
    category: str
    subcategory: str
    dataset_name: str
    coverage: float
    user_count: int
    alpha_count: int
    table_name: str

class ElasticsearchSearcher:
    """Elasticsearch搜索器"""
    
    def __init__(self, 
                 es_host: str = "localhost",
                 es_port: int = 9200,
                 index_name: str = "financial_fields",
                 data_dir: str = "split_files",
                 chinese_dir: str = "chinese_tables"):
        """
        初始化Elasticsearch搜索器
        
        Args:
            es_host: Elasticsearch主机
            es_port: Elasticsearch端口
            index_name: 索引名称
            data_dir: 原始数据目录
            chinese_dir: 中文翻译数据目录
        """
        self.es_host = es_host
        self.es_port = es_port
        self.index_name = index_name
        self.data_dir = Path(data_dir)
        self.chinese_dir = Path(chinese_dir)
        
        # 初始化Elasticsearch客户端
        self.es = None
        self._init_elasticsearch()
    
    def _init_elasticsearch(self):
        """初始化Elasticsearch连接"""
        try:
            self.es = Elasticsearch([{
                'host': self.es_host,
                'port': self.es_port,
                'scheme': 'http'
            }])
            
            # 测试连接
            if self.es.ping():
                print("✅ Elasticsearch连接成功")
            else:
                print("❌ Elasticsearch连接失败")
                self.es = None
                
        except Exception as e:
            print(f"❌ Elasticsearch初始化失败: {e}")
            print("请确保Elasticsearch服务正在运行")
            self.es = None
    
    def create_index(self):
        """创建Elasticsearch索引"""
        if not self.es:
            raise Exception("Elasticsearch未连接")
        
        # 删除现有索引
        if self.es.indices.exists(index=self.index_name):
            self.es.indices.delete(index=self.index_name)
            print(f"已删除现有索引: {self.index_name}")
        
        # 定义索引映射
        mapping = {
            "mappings": {
                "properties": {
                    "id": {
                        "type": "keyword"
                    },
                    "description": {
                        "type": "text",
                        "analyzer": "english",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "description_zh": {
                        "type": "text",
                        "analyzer": "ik_max_word",  # 中文分词器
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "region": {"type": "keyword"},
                    "universe": {"type": "keyword"},
                    "type": {"type": "keyword"},
                    "category": {"type": "keyword"},
                    "subcategory": {"type": "keyword"},
                    "dataset_name": {"type": "keyword"},
                    "table_name": {"type": "keyword"},
                    "coverage": {"type": "float"},
                    "user_count": {"type": "integer"},
                    "alpha_count": {"type": "integer"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "chinese_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop"]
                        }
                    }
                }
            }
        }
        
        # 创建索引
        self.es.indices.create(index=self.index_name, body=mapping)
        print(f"✅ 已创建索引: {self.index_name}")
    
    def index_data(self, use_chinese: bool = True):
        """索引数据到Elasticsearch"""
        if not self.es:
            raise Exception("Elasticsearch未连接")
        
        print("开始索引数据...")
        
        # 准备批量索引的数据
        actions = []
        
        # 选择数据源
        if use_chinese and self.chinese_dir.exists():
            data_files = list(self.chinese_dir.glob("zh_*.csv"))
            print(f"使用中文翻译数据，共 {len(data_files)} 个文件")
        else:
            data_files = list(self.data_dir.glob("*.csv"))
            print(f"使用原始英文数据，共 {len(data_files)} 个文件")
        
        total_records = 0
        
        for file_path in data_files:
            try:
                df = pd.read_csv(file_path)
                
                # 确定表名
                if use_chinese:
                    table_name = file_path.name.replace("zh_", "").replace(".csv", "")
                else:
                    table_name = file_path.stem
                
                print(f"处理文件: {file_path.name} ({len(df)} 条记录)")
                
                for _, row in df.iterrows():
                    doc = {
                        "id": str(row.get('id', '')),
                        "description": str(row.get('description', '')),
                        "description_zh": str(row.get('description_zh', row.get('description', ''))),
                        "region": str(row.get('region', '')),
                        "universe": str(row.get('universe', '')),
                        "type": str(row.get('type', '')),
                        "category": str(row.get('category.name', '')),
                        "subcategory": str(row.get('subcategory.name', '')),
                        "dataset_name": str(row.get('dataset.name', '')),
                        "table_name": table_name,
                        "coverage": float(row.get('coverage', 0.0)),
                        "user_count": int(row.get('userCount', 0)),
                        "alpha_count": int(row.get('alphaCount', 0))
                    }
                    
                    action = {
                        "_index": self.index_name,
                        "_source": doc
                    }
                    actions.append(action)
                    total_records += 1
                
                # 批量索引（每1000条记录）
                if len(actions) >= 1000:
                    bulk(self.es, actions)
                    print(f"已索引 {total_records} 条记录")
                    actions = []
                    
            except Exception as e:
                print(f"处理文件 {file_path.name} 失败: {e}")
        
        # 索引剩余数据
        if actions:
            bulk(self.es, actions)
        
        # 刷新索引
        self.es.indices.refresh(index=self.index_name)
        
        print(f"✅ 数据索引完成，共索引 {total_records} 条记录")
    
    def search(self, 
               query: str,
               language: str = "auto",
               top_k: int = 10,
               region_filter: Optional[str] = None,
               type_filter: Optional[str] = None,
               table_filter: Optional[str] = None,
               category_filter: Optional[str] = None) -> List[ESSearchResult]:
        """
        执行搜索
        
        Args:
            query: 搜索查询
            language: 搜索语言 ("en", "zh", "auto")
            top_k: 返回结果数量
            region_filter: 地区过滤
            type_filter: 类型过滤
            table_filter: 表格过滤
            category_filter: 分类过滤
        
        Returns:
            搜索结果列表
        """
        if not self.es:
            raise Exception("Elasticsearch未连接")
        
        if not query.strip():
            return []
        
        # 构建搜索查询
        search_body = self._build_search_query(
            query, language, region_filter, type_filter, 
            table_filter, category_filter
        )
        
        try:
            # 执行搜索
            response = self.es.search(
                index=self.index_name,
                body=search_body,
                size=top_k
            )
            
            # 解析结果
            results = []
            for hit in response['hits']['hits']:
                source = hit['_source']
                
                result = ESSearchResult(
                    id=source.get('id', ''),
                    description=source.get('description', ''),
                    description_zh=source.get('description_zh', ''),
                    similarity_score=hit['_score'],
                    region=source.get('region', ''),
                    universe=source.get('universe', ''),
                    type=source.get('type', ''),
                    category=source.get('category', ''),
                    subcategory=source.get('subcategory', ''),
                    dataset_name=source.get('dataset_name', ''),
                    coverage=source.get('coverage', 0.0),
                    user_count=source.get('user_count', 0),
                    alpha_count=source.get('alpha_count', 0),
                    table_name=source.get('table_name', '')
                )
                results.append(result)
            
            print(f"搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return []
    
    def _build_search_query(self, 
                           query: str, 
                           language: str,
                           region_filter: Optional[str],
                           type_filter: Optional[str],
                           table_filter: Optional[str],
                           category_filter: Optional[str]) -> Dict[str, Any]:
        """构建Elasticsearch搜索查询"""
        
        # 检测查询语言
        if language == "auto":
            # 简单的中文检测
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
            language = "zh" if has_chinese else "en"
        
        # 构建多字段搜索
        if language == "zh":
            # 中文搜索：优先搜索中文描述，同时搜索ID
            search_fields = [
                "description_zh^3",  # 中文描述权重最高
                "id^2",              # ID权重中等
                "description^1"      # 英文描述权重最低
            ]
        else:
            # 英文搜索：优先搜索ID，然后英文描述
            search_fields = [
                "id^3",              # ID权重最高
                "description^2",     # 英文描述权重中等
                "description_zh^1"   # 中文描述权重最低
            ]
        
        # 构建查询体
        query_body = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": query,
                                "fields": search_fields,
                                "type": "best_fields",
                                "fuzziness": "AUTO"
                            }
                        }
                    ],
                    "filter": []
                }
            },
            "highlight": {
                "fields": {
                    "description": {},
                    "description_zh": {},
                    "id": {}
                }
            }
        }
        
        # 添加过滤条件
        if region_filter:
            query_body["query"]["bool"]["filter"].append({
                "term": {"region": region_filter}
            })
        
        if type_filter:
            query_body["query"]["bool"]["filter"].append({
                "term": {"type": type_filter}
            })
        
        if table_filter:
            query_body["query"]["bool"]["filter"].append({
                "term": {"table_name": table_filter}
            })
        
        if category_filter:
            query_body["query"]["bool"]["filter"].append({
                "term": {"category": category_filter}
            })
        
        return query_body
    
    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        if not self.es:
            return {}
        
        try:
            stats = self.es.indices.stats(index=self.index_name)
            count = self.es.count(index=self.index_name)
            
            return {
                "total_documents": count["count"],
                "index_size": stats["indices"][self.index_name]["total"]["store"]["size_in_bytes"],
                "search_time": stats["indices"][self.index_name]["total"]["search"]["time_in_millis"],
                "search_count": stats["indices"][self.index_name]["total"]["search"]["query_total"]
            }
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}


def main():
    """主函数 - 演示Elasticsearch搜索功能"""
    print("🔍 Elasticsearch搜索器演示")
    print("=" * 50)
    
    try:
        # 创建搜索器
        searcher = ElasticsearchSearcher()
        
        if not searcher.es:
            print("❌ Elasticsearch未连接，请先启动Elasticsearch服务")
            print("启动命令: docker run -d --name elasticsearch -p 9200:9200 -e 'discovery.type=single-node' elasticsearch:7.14.0")
            return
        
        # 创建索引
        print("\n1. 创建索引...")
        searcher.create_index()
        
        # 索引数据（使用原始数据进行演示）
        print("\n2. 索引数据...")
        searcher.index_data(use_chinese=False)  # 先使用英文数据
        
        # 获取统计信息
        print("\n3. 索引统计:")
        stats = searcher.get_index_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # 测试搜索
        print("\n4. 测试搜索功能:")
        test_queries = [
            ("market", "en"),
            ("price", "en"),
            ("volume", "en"),
            ("capitalization", "en")
        ]
        
        for query, lang in test_queries:
            print(f"\n搜索: '{query}' (语言: {lang})")
            results = searcher.search(query, language=lang, top_k=3)
            
            for i, result in enumerate(results, 1):
                print(f"  {i}. [{result.similarity_score:.4f}] {result.id}: {result.description[:50]}...")
        
        print("\n🎉 Elasticsearch搜索器演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
