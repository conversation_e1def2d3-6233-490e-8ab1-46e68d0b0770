#!/usr/bin/env python3
"""
基于BERT的语义搜索器
使用预训练BERT模型进行中英文语义映射
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional, Set
from pathlib import Path
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from fixed_searcher import FixedFieldSearcher, SearchResult
import warnings
warnings.filterwarnings('ignore')

# 尝试导入sentence-transformers，如果没有则使用备用方案
try:
    from sentence_transformers import SentenceTransformer
    BERT_AVAILABLE = True
    print("✅ 检测到sentence-transformers，将使用BERT语义匹配")
except ImportError:
    BERT_AVAILABLE = False
    print("⚠️  未检测到sentence-transformers，将使用增强版语义匹配")
    print("💡 要使用BERT功能，请运行: pip install sentence-transformers")

class BertSemanticSearcher(FixedFieldSearcher):
    """基于BERT的语义搜索器"""
    
    def __init__(self, data_dir: str = "split_files"):
        # 先调用父类初始化
        super().__init__(data_dir)
        
        # 构建英文词汇池
        self._build_english_vocabulary_pool()
        
        # 初始化BERT模型（如果可用）
        self._initialize_bert_model()
        
        # 构建语义映射
        self._build_semantic_mapping()
        
        print(f"语义搜索器初始化完成，词汇池大小: {len(self.english_vocab_pool)}")
    
    def _build_english_vocabulary_pool(self):
        """构建英文词汇池"""
        print("正在构建英文词汇池...")
        
        self.english_vocab_pool = set()
        
        # 从所有描述中提取英文词汇
        for desc in self.descriptions:
            # 提取英文单词和短语
            english_words = re.findall(r'\b[a-zA-Z]{2,}\b', desc.lower())
            self.english_vocab_pool.update(english_words)
            
            # 提取常见的金融短语
            phrases = re.findall(r'\b[a-zA-Z]+\s+[a-zA-Z]+\b', desc.lower())
            for phrase in phrases:
                if any(keyword in phrase for keyword in ['market', 'cash', 'earnings', 'dividend', 'yield', 'flow', 'price', 'volume']):
                    self.english_vocab_pool.add(phrase)
        
        # 过滤停用词
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'must', 'can', 'shall', 'it', 'its', 'an', 'as'
        }
        
        # 过滤并排序
        self.english_vocab_pool = {
            word for word in self.english_vocab_pool 
            if word not in stop_words and len(word) >= 3
        }
        
        self.english_vocab_list = sorted(list(self.english_vocab_pool))
        print(f"英文词汇池构建完成，共 {len(self.english_vocab_pool)} 个词汇")
    
    def _initialize_bert_model(self):
        """初始化BERT模型"""
        if BERT_AVAILABLE:
            try:
                print("正在加载BERT模型...")
                # 使用多语言BERT模型，支持中英文
                self.bert_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                
                # 预计算英文词汇的BERT嵌入
                print("正在计算英文词汇的BERT嵌入...")
                self.english_embeddings = self.bert_model.encode(self.english_vocab_list, show_progress_bar=True)
                
                self.use_bert = True
                print("✅ BERT模型加载完成")
            except Exception as e:
                print(f"⚠️  BERT模型加载失败: {e}")
                self.use_bert = False
        else:
            self.use_bert = False
    
    def _build_semantic_mapping(self):
        """构建中英文语义映射表"""
        print("正在构建语义映射...")
        
        # 扩展的基础语义映射
        self.base_semantic_mapping = {
            # 基础金融概念
            '价格': ['price', 'cost', 'value', 'pricing', 'quote'],
            '市值': ['market cap', 'capitalization', 'mktcap', 'market value'],
            '成交量': ['volume', 'trading volume', 'turnover', 'transaction volume'],
            '现金流': ['cash flow', 'cashflow', 'cash stream'],
            '收入': ['revenue', 'income', 'earnings', 'sales'],
            '利润': ['profit', 'earnings', 'income', 'gain', 'net income'],
            '资产': ['asset', 'assets', 'property', 'holdings'],
            '负债': ['liability', 'debt', 'obligation', 'payable'],
            '股价': ['stock price', 'share price', 'equity price'],
            '股息': ['dividend', 'dividend yield', 'payout', 'distribution'],
            '收益': ['earnings', 'return', 'yield', 'profit', 'income'],
            '收益率': ['yield', 'return rate', 'earnings yield', 'rate of return'],
            '增长': ['growth', 'increase', 'expansion', 'growth rate'],
            '风险': ['risk', 'volatility', 'beta', 'variance'],
            '回报': ['return', 'yield', 'performance', 'gain'],
            '波动': ['volatility', 'variance', 'fluctuation', 'deviation'],
            '比率': ['ratio', 'rate', 'percentage', 'proportion'],
            '指标': ['indicator', 'metric', 'measure', 'index'],
            '财务': ['financial', 'finance', 'fiscal', 'monetary'],
            '会计': ['accounting', 'bookkeeping', 'financial reporting'],
            '估值': ['valuation', 'value', 'worth', 'appraisal'],
            '分析': ['analysis', 'analytics', 'evaluation', 'assessment'],
            '预测': ['forecast', 'prediction', 'estimate', 'projection'],
            '趋势': ['trend', 'direction', 'movement', 'pattern'],
            '表现': ['performance', 'result', 'outcome', 'achievement'],
            
            # 投资工具
            '基金': ['fund', 'mutual fund', 'investment fund'],
            '债券': ['bond', 'debenture', 'security', 'fixed income'],
            '期权': ['option', 'derivative', 'warrant', 'call', 'put'],
            '期货': ['future', 'forward', 'contract', 'commodity'],
            '股票': ['stock', 'share', 'equity', 'security'],
            
            # 市场概念
            '汇率': ['exchange rate', 'currency', 'forex', 'fx'],
            '通胀': ['inflation', 'cpi', 'price index'],
            '利率': ['interest rate', 'rate', 'yield', 'coupon'],
            '银行': ['bank', 'banking', 'financial institution'],
            '保险': ['insurance', 'coverage', 'policy', 'premium'],
            '投资': ['investment', 'invest', 'portfolio', 'allocation'],
            '交易': ['trading', 'transaction', 'deal', 'trade'],
            '市场': ['market', 'marketplace', 'exchange', 'trading'],
            
            # 公司财务
            '行业': ['industry', 'sector', 'business', 'vertical'],
            '公司': ['company', 'corporation', 'firm', 'enterprise'],
            '股东': ['shareholder', 'stockholder', 'equity holder'],
            '管理': ['management', 'administration', 'governance'],
            '策略': ['strategy', 'strategic', 'plan', 'approach'],
            '竞争': ['competition', 'competitive', 'rival', 'competitor'],
            '创新': ['innovation', 'innovative', 'development', 'r&d'],
            '技术': ['technology', 'technical', 'tech', 'innovation'],
            
            # 时间概念
            '季度': ['quarter', 'quarterly', 'q1', 'q2', 'q3', 'q4'],
            '年度': ['annual', 'yearly', 'year', 'fy'],
            '月度': ['monthly', 'month', 'period'],
            '日度': ['daily', 'day', 'date'],
            '历史': ['historical', 'history', 'past', 'historic'],
            '当前': ['current', 'present', 'latest', 'recent'],
            '未来': ['future', 'forward', 'projected', 'forecast'],
            
            # 财务比率
            '市盈率': ['pe ratio', 'price earnings', 'p/e'],
            '市净率': ['pb ratio', 'price book', 'p/b'],
            '资产负债率': ['debt ratio', 'leverage', 'debt to asset'],
            '流动比率': ['current ratio', 'liquidity ratio'],
            '速动比率': ['quick ratio', 'acid test'],
            '毛利率': ['gross margin', 'gross profit margin'],
            '净利率': ['net margin', 'net profit margin'],
            '资产回报率': ['roa', 'return on assets'],
            '股本回报率': ['roe', 'return on equity'],
        }
    
    def _find_bert_semantic_matches(self, chinese_word: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """使用BERT找到语义最相近的英文词"""
        if not self.use_bert:
            return []
        
        try:
            # 计算中文词的BERT嵌入
            chinese_embedding = self.bert_model.encode([chinese_word])
            
            # 计算与所有英文词汇的相似度
            similarities = cosine_similarity(chinese_embedding, self.english_embeddings).flatten()
            
            # 获取最相似的词汇
            top_indices = np.argsort(similarities)[::-1][:top_k * 2]
            
            matches = []
            for idx in top_indices:
                if similarities[idx] > 0.3:  # BERT相似度阈值
                    english_word = self.english_vocab_list[idx]
                    # 额外过滤：确保是有意义的金融词汇
                    if self._is_financial_term(english_word):
                        matches.append((english_word, similarities[idx]))
            
            return matches[:top_k]
        except Exception as e:
            print(f"BERT匹配出错: {e}")
            return []
    
    def _is_financial_term(self, word: str) -> bool:
        """判断是否为金融相关词汇"""
        financial_keywords = {
            'market', 'price', 'value', 'cost', 'revenue', 'income', 'profit', 'earnings',
            'dividend', 'yield', 'return', 'growth', 'risk', 'volatility', 'beta',
            'asset', 'liability', 'debt', 'equity', 'stock', 'share', 'bond',
            'fund', 'investment', 'portfolio', 'trading', 'volume', 'cash', 'flow',
            'ratio', 'margin', 'rate', 'financial', 'fiscal', 'monetary', 'economic',
            'analysis', 'forecast', 'trend', 'performance', 'indicator', 'metric',
            'quarter', 'annual', 'monthly', 'daily', 'cap', 'capitalization'
        }
        
        word_lower = word.lower()
        return any(keyword in word_lower for keyword in financial_keywords) or len(word) >= 4
    
    def _find_semantic_matches(self, chinese_word: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """综合方法找到语义匹配"""
        matches = []
        
        # 1. 首先检查基础映射
        if chinese_word in self.base_semantic_mapping:
            base_matches = self.base_semantic_mapping[chinese_word]
            for word in base_matches:
                if word in self.english_vocab_pool:
                    matches.append((word, 1.0))
        
        # 2. 如果有BERT，使用BERT匹配
        if self.use_bert and len(matches) < top_k:
            bert_matches = self._find_bert_semantic_matches(chinese_word, top_k - len(matches))
            matches.extend(bert_matches)
        
        # 3. 如果还不够，使用字符相似度
        if len(matches) < top_k:
            char_matches = self._find_character_similarity_matches(chinese_word, top_k - len(matches))
            matches.extend(char_matches)
        
        # 去重并排序
        unique_matches = {}
        for word, score in matches:
            if word not in unique_matches or score > unique_matches[word]:
                unique_matches[word] = score
        
        sorted_matches = sorted(unique_matches.items(), key=lambda x: x[1], reverse=True)
        return sorted_matches[:top_k]
    
    def _find_character_similarity_matches(self, chinese_word: str, top_k: int) -> List[Tuple[str, float]]:
        """基于字符相似度的匹配（备用方案）"""
        from difflib import SequenceMatcher
        
        matches = []
        for english_word in self.english_vocab_list:
            if self._is_financial_term(english_word):
                # 计算字符相似度
                similarity = SequenceMatcher(None, chinese_word, english_word).ratio()
                if similarity > 0.1:
                    matches.append((english_word, similarity * 0.5))  # 降低权重
        
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches[:top_k]
    
    def _expand_chinese_query_bert(self, query: str) -> str:
        """使用BERT语义匹配扩展中文查询"""
        # 中文分词
        chinese_words = list(jieba.cut(query))
        chinese_words = [word.strip() for word in chinese_words if len(word.strip()) > 1]
        
        expanded_terms = []
        
        for chinese_word in chinese_words:
            # 如果是英文词，直接保留
            if re.match(r'^[a-zA-Z\s]+$', chinese_word):
                expanded_terms.append(chinese_word)
                continue
            
            # 如果是中文词，寻找语义匹配
            if re.search(r'[\u4e00-\u9fff]', chinese_word):
                semantic_matches = self._find_semantic_matches(chinese_word, top_k=3)
                
                if semantic_matches:
                    # 添加匹配的英文词
                    matched_words = [match[0] for match in semantic_matches]
                    expanded_terms.extend(matched_words)
                    
                    print(f"中文词 '{chinese_word}' 映射到: {matched_words}")
                else:
                    # 如果没有找到语义匹配，保留原词
                    expanded_terms.append(chinese_word)
        
        # 构建扩展查询
        if expanded_terms:
            expanded_query = query + ' ' + ' '.join(expanded_terms)
            return expanded_query
        
        return query
    
    def _calculate_bert_enhanced_similarity(self, query: str) -> np.ndarray:
        """计算BERT增强的相似度"""
        # 检查是否包含中文
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', query))
        
        if has_chinese:
            # 使用BERT语义扩展
            expanded_query = self._expand_chinese_query_bert(query)
            print(f"原查询: '{query}' -> 扩展查询: '{expanded_query}'")
        else:
            expanded_query = query
        
        # 使用父类的方法计算相似度
        simple_sim = self._calculate_simple_similarity(expanded_query)
        cosine_sim = self._calculate_enhanced_cosine_similarity(expanded_query)
        
        # 如果是中文查询且使用了BERT，给予更高权重
        if has_chinese and self.use_bert:
            similarities = 0.9 * simple_sim + 0.1 * cosine_sim
        elif has_chinese:
            similarities = 0.8 * simple_sim + 0.2 * cosine_sim
        else:
            similarities = 0.7 * simple_sim + 0.3 * cosine_sim
        
        return similarities
    
    def search(self, 
               query: str, 
               top_k: int = 10,
               similarity_method: str = 'bert_enhanced',
               min_score: float = 0.01,
               region_filter: Optional[str] = None,
               type_filter: Optional[str] = None,
               table_filter: Optional[str] = None,
               category_filter: Optional[str] = None) -> List[SearchResult]:
        """
        执行BERT增强搜索
        """
        if not query.strip():
            return []
        
        print(f"BERT语义搜索: '{query}' (方法: {similarity_method})")
        
        # 计算相似度
        if similarity_method == 'bert_enhanced':
            similarities = self._calculate_bert_enhanced_similarity(query)
        else:
            # 使用父类的方法
            return super().search(query, top_k, similarity_method, min_score, 
                                region_filter, type_filter, table_filter, category_filter)
        
        # 应用过滤器
        mask = similarities >= min_score
        if region_filter:
            mask &= (self.data['region'] == region_filter)
        if type_filter:
            mask &= (self.data['type'] == type_filter)
        if table_filter:
            mask &= (self.data['table_name'] == table_filter)
        if category_filter:
            mask &= (self.data['category.name'] == category_filter)
        
        # 获取有效索引
        valid_indices = np.where(mask)[0]
        valid_similarities = similarities[mask]
        
        if len(valid_similarities) == 0:
            print("找到 0 个匹配结果")
            return []
        
        # 排序并获取top_k结果
        sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
        
        # 构建结果
        results = []
        for idx in sorted_indices:
            original_idx = valid_indices[idx]
            row = self.data.iloc[original_idx]
            
            result = SearchResult(
                id=row['id'],
                description=row['description'],
                similarity_score=valid_similarities[idx],
                region=row.get('region', ''),
                universe=row.get('universe', ''),
                type=row.get('type', ''),
                category=row.get('category.name', ''),
                subcategory=row.get('subcategory.name', ''),
                dataset_name=row.get('dataset.name', ''),
                coverage=row.get('coverage', 0.0),
                user_count=row.get('userCount', 0),
                alpha_count=row.get('alphaCount', 0)
            )
            result.table_name = row.get('table_name', '')
            results.append(result)
        
        print(f"找到 {len(results)} 个匹配结果")
        return results


def main():
    """测试BERT语义搜索器"""
    print("测试BERT语义搜索器")
    print("="*60)
    
    try:
        searcher = BertSemanticSearcher()
        
        # 测试中文搜索，特别是"股息"
        chinese_queries = [
            "股息",
            "股息收益率", 
            "分红",
            "市值",
            "价格波动",
            "现金流分析",
            "收益率",
            "风险指标"
        ]
        
        print(f"\nBERT语义搜索测试:")
        print("-" * 40)
        
        for query in chinese_queries:
            print(f"\n查询: '{query}'")
            results = searcher.search(query, top_k=3, similarity_method='bert_enhanced')
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. [{result.similarity_score:.4f}] {result.id}: {result.description[:50]}...")
            else:
                print("  未找到匹配结果")
        
        print(f"\nBERT语义搜索器测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
