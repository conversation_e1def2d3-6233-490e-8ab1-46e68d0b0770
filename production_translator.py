#!/usr/bin/env python3
"""
生产级批量翻译系统
高效翻译全部420,786条记录，支持多线程、断点续传、质量控制
"""

import pandas as pd
import numpy as np
import json
import time
import os
import threading
import queue
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 翻译相关导入
from googletrans import Translator
from transformers import pipeline
import sqlite3
from tqdm import tqdm

@dataclass
class TranslationJob:
    """翻译任务"""
    id: str
    text: str
    table_name: str
    row_index: int

@dataclass
class TranslationResult:
    """翻译结果"""
    id: str
    original_text: str
    translated_text: str
    translation_method: str
    quality_score: float
    processing_time: float
    table_name: str
    row_index: int

class ProductionTranslator:
    """生产级翻译器"""
    
    def __init__(self, 
                 data_dir: str = "split_files",
                 output_dir: str = "production_chinese_tables",
                 cache_db: str = "translation_cache.db",
                 max_workers: int = 4,
                 batch_size: int = 100):
        """
        初始化生产级翻译器
        
        Args:
            data_dir: 数据目录
            output_dir: 输出目录
            cache_db: SQLite缓存数据库
            max_workers: 最大工作线程数
            batch_size: 批处理大小
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.cache_db = cache_db
        self.max_workers = max_workers
        self.batch_size = batch_size
        
        # 翻译器
        self.google_translators = []
        self.local_translator = None
        
        # 统计信息
        self.stats = {
            'total_records': 0,
            'translated_records': 0,
            'cached_records': 0,
            'failed_records': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 初始化
        self._init_database()
        self._init_translators()
    
    def _init_database(self):
        """初始化SQLite缓存数据库"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS translation_cache (
                original_text TEXT PRIMARY KEY,
                translated_text TEXT NOT NULL,
                translation_method TEXT NOT NULL,
                quality_score REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS translation_progress (
                table_name TEXT PRIMARY KEY,
                total_rows INTEGER NOT NULL,
                completed_rows INTEGER NOT NULL,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ SQLite缓存数据库初始化完成")
    
    def _init_translators(self):
        """初始化翻译器"""
        print("正在初始化翻译器...")
        
        # 初始化多个Google翻译器实例（避免API限制）
        for i in range(self.max_workers):
            try:
                translator = Translator()
                self.google_translators.append(translator)
            except Exception as e:
                print(f"Google翻译器 {i+1} 初始化失败: {e}")
        
        print(f"✅ 已初始化 {len(self.google_translators)} 个Google翻译器")
        
        # 初始化本地翻译模型
        try:
            print("正在加载本地翻译模型...")
            self.local_translator = pipeline(
                "translation", 
                model="Helsinki-NLP/opus-mt-en-zh",
                device=-1  # 使用CPU
            )
            print("✅ 本地翻译模型加载成功")
        except Exception as e:
            print(f"❌ 本地翻译模型加载失败: {e}")
    
    def _get_cached_translation(self, text: str) -> Optional[str]:
        """从缓存获取翻译"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT translated_text FROM translation_cache WHERE original_text = ?",
            (text,)
        )
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else None
    
    def _cache_translation(self, original: str, translated: str, method: str, quality: float):
        """缓存翻译结果"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute(
            """INSERT OR REPLACE INTO translation_cache 
               (original_text, translated_text, translation_method, quality_score)
               VALUES (?, ?, ?, ?)""",
            (original, translated, method, quality)
        )
        
        conn.commit()
        conn.close()
    
    def _translate_text(self, text: str, translator_id: int = 0) -> Tuple[str, str, float]:
        """
        翻译单个文本
        
        Returns:
            (translated_text, method, quality_score)
        """
        if not text or not text.strip():
            return "", "empty", 0.0
        
        text = text.strip()
        
        # 检查缓存
        cached = self._get_cached_translation(text)
        if cached:
            return cached, "cached", 1.0
        
        # 尝试本地翻译
        if self.local_translator:
            try:
                if len(text) <= 500:  # 限制文本长度
                    result = self.local_translator(text, max_length=512)
                    translated = result[0]['translation_text'].strip()
                    
                    if translated and len(translated) > 0:
                        quality = self._assess_translation_quality(text, translated)
                        self._cache_translation(text, translated, "local", quality)
                        return translated, "local", quality
            except Exception as e:
                pass  # 继续尝试Google翻译
        
        # 尝试Google翻译
        if translator_id < len(self.google_translators):
            try:
                time.sleep(0.1)  # 避免API限制
                translator = self.google_translators[translator_id]
                result = translator.translate(text, src='en', dest='zh')
                translated = result.text.strip()
                
                if translated and len(translated) > 0:
                    quality = self._assess_translation_quality(text, translated)
                    self._cache_translation(text, translated, "google", quality)
                    return translated, "google", quality
                    
            except Exception as e:
                pass
        
        # 翻译失败，返回原文
        return text, "failed", 0.0
    
    def _assess_translation_quality(self, original: str, translated: str) -> float:
        """评估翻译质量"""
        if not translated or translated == original:
            return 0.0
        
        # 简单的质量评估
        quality = 0.5  # 基础分
        
        # 长度合理性
        length_ratio = len(translated) / len(original)
        if 0.3 <= length_ratio <= 3.0:
            quality += 0.2
        
        # 包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in translated)
        if has_chinese:
            quality += 0.3
        
        return min(quality, 1.0)
    
    def _process_translation_batch(self, jobs: List[TranslationJob], worker_id: int) -> List[TranslationResult]:
        """处理翻译批次"""
        results = []
        
        for job in jobs:
            start_time = time.time()
            
            try:
                translated, method, quality = self._translate_text(job.text, worker_id)
                processing_time = time.time() - start_time
                
                result = TranslationResult(
                    id=job.id,
                    original_text=job.text,
                    translated_text=translated,
                    translation_method=method,
                    quality_score=quality,
                    processing_time=processing_time,
                    table_name=job.table_name,
                    row_index=job.row_index
                )
                results.append(result)
                
                if method == "cached":
                    self.stats['cached_records'] += 1
                elif method in ["local", "google"]:
                    self.stats['translated_records'] += 1
                else:
                    self.stats['failed_records'] += 1
                    
            except Exception as e:
                print(f"翻译失败 {job.id}: {e}")
                self.stats['failed_records'] += 1
        
        return results
    
    def translate_file(self, file_path: Path) -> bool:
        """翻译单个文件"""
        print(f"\n处理文件: {file_path.name}")
        
        try:
            # 读取数据
            df = pd.read_csv(file_path)
            table_name = file_path.stem
            
            if 'description' not in df.columns:
                print(f"跳过文件 {file_path.name}: 缺少description列")
                return False
            
            # 检查进度
            conn = sqlite3.connect(self.cache_db)
            cursor = conn.cursor()
            cursor.execute(
                "SELECT completed_rows FROM translation_progress WHERE table_name = ?",
                (table_name,)
            )
            progress = cursor.fetchone()
            completed_rows = progress[0] if progress else 0
            conn.close()
            
            # 创建翻译任务
            jobs = []
            for idx, row in df.iterrows():
                if idx < completed_rows:
                    continue  # 跳过已完成的行
                
                job = TranslationJob(
                    id=str(row.get('id', f'{table_name}_{idx}')),
                    text=str(row['description']),
                    table_name=table_name,
                    row_index=idx
                )
                jobs.append(job)
            
            if not jobs:
                print(f"文件 {file_path.name} 已完全翻译")
                return True
            
            print(f"需要翻译 {len(jobs)} 条记录（从第 {completed_rows} 行开始）")
            
            # 分批处理
            all_results = []
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 分批提交任务
                futures = []
                for i in range(0, len(jobs), self.batch_size):
                    batch = jobs[i:i + self.batch_size]
                    worker_id = i // self.batch_size % self.max_workers
                    future = executor.submit(self._process_translation_batch, batch, worker_id)
                    futures.append(future)
                
                # 收集结果
                with tqdm(total=len(futures), desc=f"翻译 {table_name}") as pbar:
                    for future in as_completed(futures):
                        try:
                            batch_results = future.result()
                            all_results.extend(batch_results)
                            pbar.update(1)
                            
                            # 更新进度
                            if batch_results:
                                max_row = max(r.row_index for r in batch_results)
                                self._update_progress(table_name, len(df), max_row + 1)
                                
                        except Exception as e:
                            print(f"批次处理失败: {e}")
                            pbar.update(1)
            
            # 保存结果
            if all_results:
                self._save_translated_file(df, all_results, file_path)
                print(f"✅ 文件 {file_path.name} 翻译完成")
                return True
            else:
                print(f"❌ 文件 {file_path.name} 翻译失败")
                return False
                
        except Exception as e:
            print(f"❌ 处理文件 {file_path.name} 失败: {e}")
            return False
    
    def _update_progress(self, table_name: str, total_rows: int, completed_rows: int):
        """更新翻译进度"""
        conn = sqlite3.connect(self.cache_db)
        cursor = conn.cursor()
        
        cursor.execute(
            """INSERT OR REPLACE INTO translation_progress 
               (table_name, total_rows, completed_rows)
               VALUES (?, ?, ?)""",
            (table_name, total_rows, completed_rows)
        )
        
        conn.commit()
        conn.close()
    
    def _save_translated_file(self, original_df: pd.DataFrame, results: List[TranslationResult], file_path: Path):
        """保存翻译后的文件"""
        # 创建翻译映射
        translation_map = {r.row_index: r.translated_text for r in results}
        
        # 添加中文列
        df_copy = original_df.copy()
        df_copy['description_zh'] = df_copy.index.map(
            lambda idx: translation_map.get(idx, df_copy.loc[idx, 'description'])
        )
        
        # 保存文件
        output_path = self.output_dir / f"zh_{file_path.name}"
        df_copy.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        print(f"已保存: {output_path}")
    
    def translate_all_files(self):
        """翻译所有文件"""
        print("🚀 开始完整数据翻译")
        print("=" * 60)
        
        self.stats['start_time'] = time.time()
        
        # 获取所有CSV文件
        csv_files = list(self.data_dir.glob("*.csv"))
        if not csv_files:
            print("❌ 未找到CSV文件")
            return
        
        print(f"找到 {len(csv_files)} 个数据文件")
        
        # 计算总记录数
        total_records = 0
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                total_records += len(df)
            except Exception as e:
                print(f"读取文件 {file_path.name} 失败: {e}")
        
        self.stats['total_records'] = total_records
        print(f"总记录数: {total_records:,}")
        
        # 翻译所有文件
        successful_files = 0
        for i, file_path in enumerate(csv_files, 1):
            print(f"\n进度: {i}/{len(csv_files)}")
            
            if self.translate_file(file_path):
                successful_files += 1
            
            # 显示统计信息
            self._print_stats()
        
        self.stats['end_time'] = time.time()
        
        print(f"\n🎉 翻译完成！")
        print(f"成功处理: {successful_files}/{len(csv_files)} 个文件")
        self._print_final_stats()
    
    def _print_stats(self):
        """打印统计信息"""
        total = self.stats['translated_records'] + self.stats['cached_records'] + self.stats['failed_records']
        if total > 0:
            print(f"统计: 翻译 {self.stats['translated_records']}, 缓存 {self.stats['cached_records']}, 失败 {self.stats['failed_records']}")
    
    def _print_final_stats(self):
        """打印最终统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time']
        total_processed = self.stats['translated_records'] + self.stats['cached_records'] + self.stats['failed_records']
        
        print("=" * 60)
        print("📊 最终统计:")
        print(f"总记录数: {self.stats['total_records']:,}")
        print(f"已处理: {total_processed:,}")
        print(f"新翻译: {self.stats['translated_records']:,}")
        print(f"缓存命中: {self.stats['cached_records']:,}")
        print(f"翻译失败: {self.stats['failed_records']:,}")
        print(f"总耗时: {duration:.2f} 秒")
        if total_processed > 0:
            print(f"平均速度: {total_processed/duration:.2f} 条/秒")
        print("=" * 60)


def main():
    """主函数"""
    print("🌐 生产级批量翻译系统")
    print("=" * 60)
    
    # 创建翻译器
    translator = ProductionTranslator(
        max_workers=4,  # 可根据机器性能调整
        batch_size=50   # 可根据内存情况调整
    )
    
    # 开始翻译
    translator.translate_all_files()


if __name__ == "__main__":
    main()
