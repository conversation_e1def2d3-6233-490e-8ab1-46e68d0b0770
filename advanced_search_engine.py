#!/usr/bin/env python3
"""
高级搜索引擎
实现语义搜索、模糊匹配、同义词扩展、拼写纠错等高级功能
"""

import pandas as pd
import numpy as np
import json
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

# 高级搜索相关导入
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from difflib import SequenceMatcher
import jieba
import jieba.analyse

@dataclass
class AdvancedSearchResult:
    """高级搜索结果"""
    id: str
    description: str
    description_zh: str
    similarity_score: float
    match_type: str
    match_details: Dict[str, Any]
    region: str
    universe: str
    type: str
    category: str
    subcategory: str
    dataset_name: str
    coverage: float
    user_count: int
    alpha_count: int
    table_name: str

class AdvancedSearchEngine:
    """高级搜索引擎"""
    
    def __init__(self, data_dir: str = "production_chinese_tables"):
        self.data_dir = Path(data_dir)
        
        # 数据存储
        self.data = None
        self.bilingual_data = None
        
        # 搜索索引
        self.tfidf_vectorizer_en = None
        self.tfidf_vectorizer_zh = None
        self.tfidf_matrix_en = None
        self.tfidf_matrix_zh = None
        self.id_index = {}
        
        # 同义词词典
        self.synonyms_en = {}
        self.synonyms_zh = {}
        
        # 拼写纠错
        self.word_frequency = Counter()
        
        # 初始化
        self._load_data()
        self._build_search_indices()
        self._load_synonyms()
        self._build_spell_checker()
    
    def _load_data(self):
        """加载数据"""
        print("📊 加载数据...")
        
        # 尝试加载中文翻译数据
        chinese_files = list(self.data_dir.glob("zh_*.csv"))
        if chinese_files:
            print(f"找到 {len(chinese_files)} 个中文翻译文件")
            all_data = []
            
            for file_path in chinese_files:
                try:
                    df = pd.read_csv(file_path)
                    df['table_name'] = file_path.stem.replace('zh_', '')
                    all_data.append(df)
                except Exception as e:
                    print(f"加载文件 {file_path.name} 失败: {e}")
            
            if all_data:
                self.bilingual_data = pd.concat(all_data, ignore_index=True)
                print(f"✅ 已加载双语数据: {len(self.bilingual_data)} 条记录")
        
        # 如果没有中文数据，加载原始数据
        if self.bilingual_data is None:
            print("未找到中文翻译数据，加载原始数据...")
            original_files = list(Path("split_files").glob("*.csv"))
            
            all_data = []
            for file_path in original_files:
                try:
                    df = pd.read_csv(file_path)
                    df['table_name'] = file_path.stem
                    # 添加空的中文列
                    df['description_zh'] = df['description']
                    all_data.append(df)
                except Exception as e:
                    print(f"加载文件 {file_path.name} 失败: {e}")
            
            if all_data:
                self.bilingual_data = pd.concat(all_data, ignore_index=True)
                print(f"✅ 已加载原始数据: {len(self.bilingual_data)} 条记录")
        
        self.data = self.bilingual_data
    
    def _build_search_indices(self):
        """构建搜索索引"""
        print("🔍 构建搜索索引...")
        
        if self.data is None:
            return
        
        # 构建ID索引
        for idx, row in self.data.iterrows():
            field_id = str(row.get('id', ''))
            if field_id:
                self.id_index[field_id.lower()] = idx
        
        print(f"✅ ID索引: {len(self.id_index)} 条")
        
        # 构建TF-IDF索引 - 英文
        try:
            english_texts = self.data['description'].fillna('').astype(str).tolist()
            self.tfidf_vectorizer_en = TfidfVectorizer(
                max_features=10000,
                stop_words='english',
                ngram_range=(1, 2),
                min_df=2,
                max_df=0.8
            )
            self.tfidf_matrix_en = self.tfidf_vectorizer_en.fit_transform(english_texts)
            print(f"✅ 英文TF-IDF索引: {self.tfidf_matrix_en.shape}")
        except Exception as e:
            print(f"❌ 英文TF-IDF索引构建失败: {e}")
        
        # 构建TF-IDF索引 - 中文
        try:
            # 中文分词
            chinese_texts = []
            for text in self.data['description_zh'].fillna('').astype(str):
                # 使用jieba分词
                words = jieba.cut(text)
                chinese_texts.append(' '.join(words))
            
            self.tfidf_vectorizer_zh = TfidfVectorizer(
                max_features=10000,
                ngram_range=(1, 2),
                min_df=2,
                max_df=0.8
            )
            self.tfidf_matrix_zh = self.tfidf_vectorizer_zh.fit_transform(chinese_texts)
            print(f"✅ 中文TF-IDF索引: {self.tfidf_matrix_zh.shape}")
        except Exception as e:
            print(f"❌ 中文TF-IDF索引构建失败: {e}")
    
    def _load_synonyms(self):
        """加载同义词词典"""
        print("📚 加载同义词词典...")
        
        # 英文同义词
        self.synonyms_en = {
            'market': ['marketplace', 'trading', 'exchange', 'commerce'],
            'price': ['cost', 'value', 'rate', 'amount', 'pricing'],
            'volume': ['quantity', 'amount', 'size', 'capacity'],
            'return': ['yield', 'profit', 'gain', 'income'],
            'ratio': ['rate', 'proportion', 'percentage'],
            'capitalization': ['cap', 'market_cap', 'mcap'],
            'earnings': ['profit', 'income', 'revenue'],
            'dividend': ['payout', 'distribution'],
            'beta': ['volatility', 'risk'],
            'growth': ['increase', 'expansion', 'development']
        }
        
        # 中文同义词
        self.synonyms_zh = {
            '市场': ['交易', '市场', '商场', '贸易'],
            '价格': ['价钱', '费用', '成本', '定价'],
            '成交量': ['交易量', '成交额', '交易额'],
            '收益': ['回报', '利润', '收入', '盈利'],
            '比率': ['比例', '百分比', '占比'],
            '市值': ['市场价值', '总市值'],
            '盈利': ['利润', '收益', '所得'],
            '股息': ['分红', '红利'],
            '波动': ['变动', '震荡', '起伏'],
            '增长': ['增加', '上涨', '提升']
        }
        
        print(f"✅ 英文同义词: {len(self.synonyms_en)} 组")
        print(f"✅ 中文同义词: {len(self.synonyms_zh)} 组")
    
    def _build_spell_checker(self):
        """构建拼写检查器"""
        print("🔤 构建拼写检查器...")
        
        # 统计词频
        all_words = set()
        
        # 从ID中提取词汇
        for field_id in self.id_index.keys():
            words = re.findall(r'[a-zA-Z]+', field_id)
            for word in words:
                if len(word) > 2:
                    all_words.add(word.lower())
                    self.word_frequency[word.lower()] += 1
        
        # 从描述中提取词汇
        if self.tfidf_vectorizer_en:
            feature_names = self.tfidf_vectorizer_en.get_feature_names_out()
            for word in feature_names:
                if len(word) > 2 and word.isalpha():
                    all_words.add(word.lower())
                    self.word_frequency[word.lower()] += 1
        
        print(f"✅ 词汇库: {len(all_words)} 个词汇")
    
    def _expand_query_with_synonyms(self, query: str, language: str) -> List[str]:
        """使用同义词扩展查询"""
        expanded_terms = [query]
        
        if language == 'en':
            synonyms_dict = self.synonyms_en
        else:
            synonyms_dict = self.synonyms_zh
        
        query_lower = query.lower()
        for term, synonyms in synonyms_dict.items():
            if term in query_lower:
                expanded_terms.extend(synonyms)
            elif query_lower in synonyms:
                expanded_terms.append(term)
                expanded_terms.extend(synonyms)
        
        return list(set(expanded_terms))
    
    def _spell_correct(self, word: str) -> str:
        """拼写纠错"""
        if word.lower() in self.word_frequency:
            return word
        
        # 找到最相似的词
        best_match = word
        best_score = 0.0
        
        for candidate in self.word_frequency.keys():
            if abs(len(candidate) - len(word)) <= 2:  # 长度差异不超过2
                score = SequenceMatcher(None, word.lower(), candidate).ratio()
                if score > best_score and score > 0.8:  # 相似度阈值
                    best_score = score
                    best_match = candidate
        
        return best_match if best_score > 0.8 else word
    
    def _semantic_search(self, query: str, language: str, top_k: int = 100) -> List[Tuple[int, float]]:
        """语义搜索"""
        if language == 'en' and self.tfidf_vectorizer_en and self.tfidf_matrix_en is not None:
            # 英文语义搜索
            try:
                query_vector = self.tfidf_vectorizer_en.transform([query])
                similarities = cosine_similarity(query_vector, self.tfidf_matrix_en).flatten()
                
                # 获取top_k结果
                top_indices = np.argsort(similarities)[::-1][:top_k]
                results = [(idx, similarities[idx]) for idx in top_indices if similarities[idx] > 0]
                
                return results
            except Exception as e:
                print(f"英文语义搜索失败: {e}")
        
        elif language == 'zh' and self.tfidf_vectorizer_zh and self.tfidf_matrix_zh is not None:
            # 中文语义搜索
            try:
                # 中文分词
                query_words = jieba.cut(query)
                query_text = ' '.join(query_words)
                
                query_vector = self.tfidf_vectorizer_zh.transform([query_text])
                similarities = cosine_similarity(query_vector, self.tfidf_matrix_zh).flatten()
                
                # 获取top_k结果
                top_indices = np.argsort(similarities)[::-1][:top_k]
                results = [(idx, similarities[idx]) for idx in top_indices if similarities[idx] > 0]
                
                return results
            except Exception as e:
                print(f"中文语义搜索失败: {e}")
        
        return []
    
    def _fuzzy_match(self, query: str, text: str, threshold: float = 0.6) -> float:
        """模糊匹配"""
        query_lower = query.lower()
        text_lower = text.lower()
        
        # 完全匹配
        if query_lower == text_lower:
            return 1.0
        
        # 包含匹配
        if query_lower in text_lower:
            return 0.8 + 0.2 * (len(query_lower) / len(text_lower))
        
        # 序列匹配
        seq_ratio = SequenceMatcher(None, query_lower, text_lower).ratio()
        if seq_ratio >= threshold:
            return seq_ratio * 0.7
        
        # 词汇匹配
        query_words = set(query_lower.split())
        text_words = set(text_lower.split())
        
        if query_words and text_words:
            intersection = query_words.intersection(text_words)
            union = query_words.union(text_words)
            jaccard = len(intersection) / len(union)
            
            if jaccard >= threshold:
                return jaccard * 0.6
        
        return 0.0
    
    def advanced_search(self, 
                       query: str,
                       top_k: int = 20,
                       language: str = "auto",
                       enable_synonyms: bool = True,
                       enable_spell_check: bool = True,
                       enable_semantic: bool = True,
                       enable_fuzzy: bool = True,
                       fuzzy_threshold: float = 0.6) -> List[AdvancedSearchResult]:
        """
        高级搜索
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            language: 查询语言
            enable_synonyms: 启用同义词扩展
            enable_spell_check: 启用拼写纠错
            enable_semantic: 启用语义搜索
            enable_fuzzy: 启用模糊匹配
            fuzzy_threshold: 模糊匹配阈值
        """
        if not query.strip():
            return []
        
        start_time = time.time()
        
        # 语言检测
        if language == "auto":
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
            detected_language = "zh" if has_chinese else "en"
        else:
            detected_language = language
        
        print(f"🔍 高级搜索: '{query}' (语言: {detected_language})")
        
        # 查询预处理
        processed_query = query.strip()
        
        # 拼写纠错
        if enable_spell_check and detected_language == "en":
            corrected_words = []
            for word in processed_query.split():
                corrected = self._spell_correct(word)
                corrected_words.append(corrected)
            corrected_query = ' '.join(corrected_words)
            
            if corrected_query != processed_query:
                print(f"拼写纠错: '{processed_query}' -> '{corrected_query}'")
                processed_query = corrected_query
        
        # 同义词扩展
        expanded_queries = [processed_query]
        if enable_synonyms:
            expanded = self._expand_query_with_synonyms(processed_query, detected_language)
            expanded_queries.extend(expanded)
            if len(expanded) > 1:
                print(f"同义词扩展: {expanded[:5]}...")  # 显示前5个
        
        # 收集所有匹配结果
        all_matches = {}  # {index: (score, match_type, details)}
        
        # 1. ID精确匹配
        query_lower = processed_query.lower()
        for field_id, idx in self.id_index.items():
            if query_lower == field_id:
                all_matches[idx] = (1.0, "id_exact", {"matched_id": field_id})
            elif query_lower in field_id:
                score = 0.8 + 0.15 * (len(query_lower) / len(field_id))
                all_matches[idx] = (score, "id_partial", {"matched_id": field_id})
        
        # 2. 语义搜索
        if enable_semantic:
            for expanded_query in expanded_queries[:3]:  # 限制扩展查询数量
                semantic_results = self._semantic_search(expanded_query, detected_language, top_k * 2)
                
                for idx, score in semantic_results:
                    if idx not in all_matches or all_matches[idx][0] < score * 0.7:
                        all_matches[idx] = (score * 0.7, "semantic", {
                            "query": expanded_query,
                            "semantic_score": score
                        })
        
        # 3. 模糊匹配
        if enable_fuzzy:
            for idx, row in self.data.iterrows():
                if idx in all_matches:
                    continue  # 跳过已匹配的
                
                # 检查ID模糊匹配
                field_id = str(row.get('id', ''))
                id_fuzzy_score = self._fuzzy_match(processed_query, field_id, fuzzy_threshold)
                
                # 检查描述模糊匹配
                if detected_language == "zh":
                    desc = str(row.get('description_zh', ''))
                else:
                    desc = str(row.get('description', ''))
                
                desc_fuzzy_score = self._fuzzy_match(processed_query, desc, fuzzy_threshold)
                
                # 取最高分
                max_fuzzy_score = max(id_fuzzy_score, desc_fuzzy_score)
                
                if max_fuzzy_score >= fuzzy_threshold:
                    match_type = "fuzzy_id" if id_fuzzy_score > desc_fuzzy_score else "fuzzy_desc"
                    all_matches[idx] = (max_fuzzy_score, match_type, {
                        "fuzzy_score": max_fuzzy_score,
                        "id_score": id_fuzzy_score,
                        "desc_score": desc_fuzzy_score
                    })
        
        # 排序和返回结果
        sorted_matches = sorted(all_matches.items(), key=lambda x: x[1][0], reverse=True)
        
        results = []
        for idx, (score, match_type, details) in sorted_matches[:top_k]:
            row = self.data.iloc[idx]
            
            result = AdvancedSearchResult(
                id=str(row.get('id', '')),
                description=str(row.get('description', '')),
                description_zh=str(row.get('description_zh', row.get('description', ''))),
                similarity_score=score,
                match_type=match_type,
                match_details=details,
                region=str(row.get('region', '')),
                universe=str(row.get('universe', '')),
                type=str(row.get('type', '')),
                category=str(row.get('category.name', '')),
                subcategory=str(row.get('subcategory.name', '')),
                dataset_name=str(row.get('dataset.name', '')),
                coverage=float(row.get('coverage', 0.0)),
                user_count=int(row.get('userCount', 0)),
                alpha_count=int(row.get('alphaCount', 0)),
                table_name=str(row.get('table_name', ''))
            )
            results.append(result)
        
        search_time = time.time() - start_time
        print(f"✅ 搜索完成: {len(results)} 个结果，耗时 {search_time:.3f}秒")
        
        return results


def main():
    """主函数 - 演示高级搜索功能"""
    print("🚀 高级搜索引擎演示")
    print("=" * 50)
    
    try:
        # 创建搜索引擎
        engine = AdvancedSearchEngine()
        
        # 测试搜索
        test_queries = [
            ("market", "英文搜索"),
            ("价格", "中文搜索"),
            ("markrt", "拼写纠错测试"),  # 故意拼错
            ("trading volume", "语义搜索"),
            ("市场资本", "中文语义搜索")
        ]
        
        for query, description in test_queries:
            print(f"\n{description}: '{query}'")
            print("-" * 30)
            
            results = engine.advanced_search(
                query,
                top_k=5,
                enable_synonyms=True,
                enable_spell_check=True,
                enable_semantic=True,
                enable_fuzzy=True
            )
            
            for i, result in enumerate(results, 1):
                match_emoji = {
                    "id_exact": "🎯",
                    "id_partial": "🔍",
                    "semantic": "🧠",
                    "fuzzy_id": "🌀",
                    "fuzzy_desc": "📝"
                }.get(result.match_type, "❓")
                
                print(f"  {i}. [{result.similarity_score:.4f}] {match_emoji} {result.id}")
                print(f"     {result.match_type}: {result.description[:50]}...")
        
        print("\n🎉 高级搜索引擎演示完成！")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
