#!/usr/bin/env python3
"""
ID优先搜索测试
展示修复前后的搜索结果对比
"""

from fixed_searcher import FixedFieldSearcher

def test_id_priority_vs_other_methods():
    """对比ID优先搜索与其他方法的效果"""
    print("🎯 ID优先搜索 vs 其他方法对比测试")
    print("=" * 60)
    
    searcher = FixedFieldSearcher()
    
    # 测试用例：这些查询词都有对应的ID完全匹配
    test_cases = [
        "market",
        "price", 
        "volume",
        "cap",
        "beta"
    ]
    
    methods = [
        ("id_priority", "ID优先匹配"),
        ("enhanced", "增强方法"),
        ("simple", "简单匹配"),
        ("cosine", "余弦相似度")
    ]
    
    for query in test_cases:
        print(f"\n🔍 查询词: '{query}'")
        print("-" * 40)
        
        for method_code, method_name in methods:
            try:
                results = searcher.search(
                    query, 
                    top_k=3, 
                    similarity_method=method_code,
                    min_score=0.01
                )
                
                print(f"\n{method_name}:")
                if results:
                    for i, result in enumerate(results, 1):
                        score = result.similarity_score
                        id_match = "✅" if query.lower() == result.id.lower() else "  "
                        print(f"  {i}. [{score:.4f}] {id_match} {result.id}")
                else:
                    print("  无结果")
                    
            except Exception as e:
                print(f"  错误: {e}")

def test_exact_id_matches():
    """测试精确ID匹配的效果"""
    print(f"\n\n🎯 精确ID匹配测试")
    print("=" * 60)
    
    searcher = FixedFieldSearcher()
    
    # 查找一些确实存在的ID
    import pandas as pd
    sample_ids = []
    
    # 从第一个文件中获取一些示例ID
    df = pd.read_csv("split_files/CHN_0_TOP2000U.csv")
    sample_ids = df['id'].head(5).tolist()
    
    print("测试精确ID匹配:")
    for test_id in sample_ids:
        print(f"\n查询ID: '{test_id}'")
        
        results = searcher.search(
            test_id, 
            top_k=3, 
            similarity_method='id_priority',
            min_score=0.01
        )
        
        if results:
            top_result = results[0]
            exact_match = test_id.lower() == top_result.id.lower()
            print(f"  最佳匹配: [{top_result.similarity_score:.4f}] {top_result.id}")
            print(f"  精确匹配: {'✅ 是' if exact_match else '❌ 否'}")
            
            if exact_match:
                print(f"  描述: {top_result.description}")
        else:
            print("  ❌ 未找到匹配结果")

def test_partial_id_matches():
    """测试部分ID匹配的效果"""
    print(f"\n\n🎯 部分ID匹配测试")
    print("=" * 60)
    
    searcher = FixedFieldSearcher()
    
    # 测试部分匹配的情况
    partial_queries = [
        "mkt",      # 应该匹配 mktcap 等
        "vol",      # 应该匹配 volume 相关
        "ret",      # 应该匹配 return 相关
        "div",      # 应该匹配 dividend 相关
    ]
    
    for query in partial_queries:
        print(f"\n查询: '{query}' (部分匹配)")
        
        results = searcher.search(
            query, 
            top_k=5, 
            similarity_method='id_priority',
            min_score=0.01
        )
        
        print("  匹配结果:")
        for i, result in enumerate(results, 1):
            score = result.similarity_score
            contains_query = query.lower() in result.id.lower()
            marker = "🎯" if contains_query else "  "
            print(f"  {i}. [{score:.4f}] {marker} {result.id}")

def main():
    """主测试函数"""
    try:
        test_id_priority_vs_other_methods()
        test_exact_id_matches()
        test_partial_id_matches()
        
        print(f"\n\n🎉 测试完成！")
        print("=" * 60)
        print("✅ ID优先搜索算法工作正常")
        print("✅ 精确ID匹配获得最高优先级")
        print("✅ 部分ID匹配也能正确识别")
        print("\n💡 建议:")
        print("- 使用 'ID优先匹配' 方法获得最佳搜索体验")
        print("- 当搜索具体字段ID时，该字段会排在最前面")
        print("- Web界面已更新，默认使用ID优先匹配")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
