2025-08-22 08:49:10,411 - __main__ - INFO - 正在初始化完整版搜索引擎...
2025-08-22 08:50:53,984 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://*********:5003
2025-08-22 08:50:53,987 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-22 08:50:56,208 - jieba - DEBUG - Building prefix dict from the default dictionary ...
2025-08-22 08:50:56,224 - jieba - DEBUG - Loading model from cache /var/folders/vh/d39ztmps3nb9ryyj5r2bcpr40000gn/T/jieba.cache
2025-08-22 08:50:57,927 - jieba - DEBUG - Loading model cost 1.709 seconds.
2025-08-22 08:50:57,928 - jieba - DEBUG - Prefix dict has been built successfully.
2025-08-22 08:51:14,272 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:14] "GET / HTTP/1.1" 200 -
2025-08-22 08:51:22,941 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:22] "GET / HTTP/1.1" 200 -
2025-08-22 08:51:23,259 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:23] "[31m[1mGET /api/stats HTTP/1.1[0m" 401 -
2025-08-22 08:51:25,098 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:25] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-22 08:51:27,097 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:27] "GET /login HTTP/1.1" 200 -
2025-08-22 08:51:33,418 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:33] "POST /api/login HTTP/1.1" 200 -
2025-08-22 08:51:34,823 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 08:51:34] "POST /api/login HTTP/1.1" 200 -
2025-08-22 08:55:17,030 - __main__ - INFO - ✅ 完整版搜索引擎初始化完成
2025-08-22 08:55:17,057 - __main__ - INFO - 已创建默认管理员用户: admin/admin123
