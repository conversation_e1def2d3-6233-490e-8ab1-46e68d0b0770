#!/usr/bin/env python3
"""
优化版数据字段检索器
专门针对大规模数据和高性能搜索进行优化
"""

import pandas as pd
import numpy as np
import re
import pickle
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from field_searcher import SearchResult, FieldSearcher
import time
import warnings
warnings.filterwarnings('ignore')

class OptimizedFieldSearcher(FieldSearcher):
    """优化版字段搜索器"""
    
    def __init__(self, data_dir: str = "split_files", cache_dir: str = "cache"):
        """
        初始化优化搜索器
        
        Args:
            data_dir: 数据文件目录路径
            cache_dir: 缓存目录路径
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 尝试从缓存加载
        if self._load_from_cache():
            print("从缓存加载搜索索引完成")
        else:
            print("缓存未找到，重新构建索引...")
            super().__init__(data_dir)
            self._save_to_cache()
            print("索引已保存到缓存")
    
    def _get_cache_path(self, name: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{name}.pkl"
    
    def _save_to_cache(self):
        """保存索引到缓存"""
        try:
            # 保存数据
            with open(self._get_cache_path("data"), "wb") as f:
                pickle.dump(self.data, f)
            
            # 保存TF-IDF向量化器
            with open(self._get_cache_path("tfidf_vectorizer"), "wb") as f:
                pickle.dump(self.tfidf_vectorizer, f)
            
            # 保存TF-IDF矩阵
            with open(self._get_cache_path("tfidf_matrix"), "wb") as f:
                pickle.dump(self.tfidf_matrix, f)
            
            # 保存描述列表
            with open(self._get_cache_path("descriptions"), "wb") as f:
                pickle.dump(self.descriptions, f)
            
            print("缓存保存成功")
        except Exception as e:
            print(f"缓存保存失败: {e}")
    
    def _load_from_cache(self) -> bool:
        """从缓存加载索引"""
        try:
            cache_files = [
                "data", "tfidf_vectorizer", "tfidf_matrix", "descriptions"
            ]
            
            # 检查所有缓存文件是否存在
            for cache_file in cache_files:
                if not self._get_cache_path(cache_file).exists():
                    return False
            
            # 加载数据
            with open(self._get_cache_path("data"), "rb") as f:
                self.data = pickle.load(f)
            
            # 加载TF-IDF向量化器
            with open(self._get_cache_path("tfidf_vectorizer"), "rb") as f:
                self.tfidf_vectorizer = pickle.load(f)
            
            # 加载TF-IDF矩阵
            with open(self._get_cache_path("tfidf_matrix"), "rb") as f:
                self.tfidf_matrix = pickle.load(f)
            
            # 加载描述列表
            with open(self._get_cache_path("descriptions"), "rb") as f:
                self.descriptions = pickle.load(f)
            
            # 初始化jieba
            jieba.initialize()
            
            return True
        except Exception as e:
            print(f"缓存加载失败: {e}")
            return False
    
    def clear_cache(self):
        """清除缓存"""
        try:
            cache_files = [
                "data", "tfidf_vectorizer", "tfidf_matrix", "descriptions"
            ]
            
            for cache_file in cache_files:
                cache_path = self._get_cache_path(cache_file)
                if cache_path.exists():
                    cache_path.unlink()
            
            print("缓存已清除")
        except Exception as e:
            print(f"清除缓存失败: {e}")
    
    def _calculate_cosine_similarity_optimized(self, query: str) -> np.ndarray:
        """优化的余弦相似度计算"""
        processed_query = self._preprocess_query(query)
        query_vector = self.tfidf_vectorizer.transform([processed_query])
        
        # 使用稀疏矩阵优化计算
        similarities = (query_vector * self.tfidf_matrix.T).toarray().flatten()
        
        # 归一化
        query_norm = np.linalg.norm(query_vector.toarray())
        doc_norms = np.linalg.norm(self.tfidf_matrix.toarray(), axis=1)
        
        # 避免除零
        with np.errstate(divide='ignore', invalid='ignore'):
            similarities = similarities / (query_norm * doc_norms)
            similarities = np.nan_to_num(similarities)
        
        return similarities
    
    def search_fast(self, 
                   query: str, 
                   top_k: int = 10,
                   min_score: float = 0.1) -> List[SearchResult]:
        """
        快速搜索 - 仅使用优化的余弦相似度
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            min_score: 最小相似度阈值
        
        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []
        
        # 使用优化的余弦相似度
        similarities = self._calculate_cosine_similarity_optimized(query)
        
        # 应用阈值过滤
        mask = similarities >= min_score
        valid_indices = np.where(mask)[0]
        valid_similarities = similarities[mask]
        
        # 排序并获取top_k结果
        sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
        
        # 构建结果
        results = []
        for idx in sorted_indices:
            original_idx = valid_indices[idx]
            row = self.data.iloc[original_idx]
            
            result = SearchResult(
                id=row['id'],
                description=row['description'],
                similarity_score=valid_similarities[idx],
                region=row.get('region', ''),
                universe=row.get('universe', ''),
                type=row.get('type', ''),
                category=row.get('category.name', ''),
                subcategory=row.get('subcategory.name', ''),
                dataset_name=row.get('dataset.name', ''),
                coverage=row.get('coverage', 0.0),
                user_count=row.get('userCount', 0),
                alpha_count=row.get('alphaCount', 0)
            )
            results.append(result)
        
        return results
    
    def batch_search(self, 
                    queries: List[str], 
                    top_k: int = 10,
                    min_score: float = 0.1) -> Dict[str, List[SearchResult]]:
        """
        批量搜索
        
        Args:
            queries: 查询列表
            top_k: 每个查询返回的结果数量
            min_score: 最小相似度阈值
        
        Returns:
            查询到结果的映射字典
        """
        results = {}
        
        print(f"开始批量搜索 {len(queries)} 个查询...")
        start_time = time.time()
        
        for i, query in enumerate(queries):
            if query.strip():
                results[query] = self.search_fast(query, top_k, min_score)
                
                if (i + 1) % 10 == 0:
                    elapsed = time.time() - start_time
                    avg_time = elapsed / (i + 1)
                    print(f"已完成 {i + 1}/{len(queries)} 个查询，平均时间: {avg_time:.3f}秒")
        
        total_time = time.time() - start_time
        print(f"批量搜索完成，总时间: {total_time:.3f}秒")
        
        return results
    
    def get_similar_fields(self, field_id: str, top_k: int = 10) -> List[SearchResult]:
        """
        根据字段ID查找相似字段
        
        Args:
            field_id: 字段ID
            top_k: 返回结果数量
        
        Returns:
            相似字段列表
        """
        # 查找字段
        field_row = self.data[self.data['id'] == field_id]
        if field_row.empty:
            print(f"未找到字段ID: {field_id}")
            return []
        
        # 使用字段描述进行搜索
        description = field_row.iloc[0]['description']
        return self.search_fast(description, top_k + 1)[1:]  # 排除自身
    
    def benchmark_performance(self, num_queries: int = 100) -> Dict[str, float]:
        """
        性能基准测试
        
        Args:
            num_queries: 测试查询数量
        
        Returns:
            性能统计
        """
        # 随机选择测试查询
        sample_descriptions = self.data['description'].sample(num_queries).tolist()
        test_queries = [desc.split()[:2] for desc in sample_descriptions]  # 取前两个词
        test_queries = [' '.join(words) for words in test_queries if len(words) >= 1]
        
        print(f"开始性能测试，共 {len(test_queries)} 个查询...")
        
        # 测试快速搜索
        start_time = time.time()
        for query in test_queries:
            self.search_fast(query, top_k=10)
        fast_time = time.time() - start_time
        
        # 测试混合搜索
        start_time = time.time()
        for query in test_queries[:min(10, len(test_queries))]:  # 只测试前10个，因为混合搜索较慢
            self.search(query, top_k=10, similarity_method='hybrid')
        hybrid_time = (time.time() - start_time) * len(test_queries) / min(10, len(test_queries))
        
        stats = {
            'fast_search_total_time': fast_time,
            'fast_search_avg_time': fast_time / len(test_queries),
            'hybrid_search_estimated_total_time': hybrid_time,
            'hybrid_search_estimated_avg_time': hybrid_time / len(test_queries),
            'speedup_factor': hybrid_time / fast_time if fast_time > 0 else 0,
            'queries_per_second_fast': len(test_queries) / fast_time if fast_time > 0 else 0,
            'queries_per_second_hybrid': len(test_queries) / hybrid_time if hybrid_time > 0 else 0
        }
        
        print(f"性能测试完成:")
        print(f"快速搜索: {stats['fast_search_avg_time']:.4f}秒/查询, {stats['queries_per_second_fast']:.1f}查询/秒")
        print(f"混合搜索: {stats['hybrid_search_estimated_avg_time']:.4f}秒/查询, {stats['queries_per_second_hybrid']:.1f}查询/秒")
        print(f"性能提升: {stats['speedup_factor']:.1f}倍")
        
        return stats


def main():
    """主函数 - 演示优化搜索器"""
    try:
        # 初始化优化搜索器
        searcher = OptimizedFieldSearcher()
        
        # 性能基准测试
        print("\n" + "="*60)
        print("性能基准测试")
        print("="*60)
        searcher.benchmark_performance(50)
        
        # 快速搜索演示
        print("\n" + "="*60)
        print("快速搜索演示")
        print("="*60)
        
        test_queries = ["market cap", "cash flow", "price", "volume", "beta"]
        
        for query in test_queries:
            start_time = time.time()
            results = searcher.search_fast(query, top_k=3)
            search_time = time.time() - start_time
            
            print(f"\n查询: '{query}' (耗时: {search_time:.4f}秒)")
            for i, result in enumerate(results, 1):
                print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")
        
        # 批量搜索演示
        print("\n" + "="*60)
        print("批量搜索演示")
        print("="*60)
        
        batch_queries = ["revenue", "assets", "debt", "growth", "risk"]
        batch_results = searcher.batch_search(batch_queries, top_k=2)
        
        for query, results in batch_results.items():
            print(f"\n查询: '{query}'")
            for i, result in enumerate(results, 1):
                print(f"  {i}. [{result.similarity_score:.4f}] {result.id}")
        
        # 相似字段查找演示
        print("\n" + "="*60)
        print("相似字段查找演示")
        print("="*60)
        
        # 随机选择一个字段
        sample_field = searcher.data.sample(1).iloc[0]
        field_id = sample_field['id']
        
        print(f"查找与字段 '{field_id}' 相似的字段:")
        print(f"原字段描述: {sample_field['description']}")
        
        similar_fields = searcher.get_similar_fields(field_id, top_k=3)
        for i, result in enumerate(similar_fields, 1):
            print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
