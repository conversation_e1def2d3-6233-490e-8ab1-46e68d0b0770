#!/usr/bin/env python3
"""
快速启动Web应用 - 不使用翻译功能
"""

from flask import Flask, render_template, request, jsonify, send_file
from fixed_searcher import FixedFieldSearcher
import pandas as pd
import io
import time
import traceback

app = Flask(__name__)

# 全局搜索器实例
searcher = None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/init', methods=['POST'])
def init_searcher():
    """初始化搜索器"""
    global searcher
    
    try:
        if searcher is None:
            print("正在初始化搜索器（不使用翻译功能）...")
            searcher = FixedFieldSearcher(enable_translation=False)
            print("搜索器初始化完成")
        
        # 返回统计信息
        stats = {
            'total_records': len(searcher.data),
            'unique_tables': len(searcher.tables),
            'regions': list(searcher.data['region'].unique()),
            'categories': list(searcher.data['category.name'].unique()) if 'category.name' in searcher.data.columns else []
        }
        
        return jsonify({
            'success': True,
            'message': '搜索器初始化成功',
            'stats': stats
        })
        
    except Exception as e:
        print(f"初始化失败: {e}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search', methods=['POST'])
def search():
    """搜索API"""
    global searcher
    
    try:
        if searcher is None:
            return jsonify({
                'success': False,
                'error': '搜索器未初始化，请先调用 /api/init'
            }), 400
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                'success': False,
                'error': '查询不能为空'
            }), 400
        
        # 搜索参数
        top_k = data.get('top_k', 10)
        similarity_method = data.get('similarity_method', 'smart')
        min_score = data.get('min_score', 0.01)
        
        # 过滤参数
        table_filter = data.get('table_filter')
        region_filter = data.get('region_filter')
        type_filter = data.get('type_filter')
        category_filter = data.get('category_filter')
        
        print(f"搜索查询: '{query}' (方法: {similarity_method})")
        
        # 执行搜索
        start_time = time.time()
        results = searcher.search(
            query=query,
            top_k=top_k,
            similarity_method=similarity_method,
            min_score=min_score,
            table_filter=table_filter,
            region_filter=region_filter,
            type_filter=type_filter,
            category_filter=category_filter
        )
        search_time = time.time() - start_time
        
        print(f"找到 {len(results)} 个匹配结果")
        
        # 转换结果为JSON格式
        results_json = []
        for result in results:
            results_json.append({
                'id': str(result.id),
                'description': str(result.description),
                'similarity_score': float(result.similarity_score),
                'region': str(result.region),
                'universe': str(result.universe),
                'type': str(result.type),
                'category': str(result.category),
                'subcategory': str(result.subcategory),
                'dataset_name': str(result.dataset_name)
            })
        
        return jsonify({
            'success': True,
            'results': results_json,
            'search_time': search_time,
            'total_results': len(results),
            'query': query,
            'method': similarity_method
        })
        
    except Exception as e:
        print(f"搜索失败: {e}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export', methods=['POST'])
def export_results():
    """导出搜索结果"""
    try:
        data = request.get_json()
        results = data.get('results', [])
        
        if not results:
            return jsonify({
                'success': False,
                'error': '没有结果可导出'
            }), 400
        
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 创建CSV内容
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        csv_content = output.getvalue()
        output.close()
        
        # 创建文件对象
        csv_buffer = io.BytesIO()
        csv_buffer.write(csv_content.encode('utf-8'))
        csv_buffer.seek(0)
        
        return send_file(
            csv_buffer,
            mimetype='text/csv',
            as_attachment=True,
            download_name='search_results.csv'
        )
        
    except Exception as e:
        print(f"导出失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    global searcher
    
    try:
        if searcher is None:
            return jsonify({
                'success': False,
                'error': '搜索器未初始化'
            }), 400
        
        stats = {
            'total_records': len(searcher.data),
            'unique_tables': len(searcher.tables),
            'regions': list(searcher.data['region'].unique()),
            'types': list(searcher.data['type'].unique()),
            'categories': list(searcher.data['category.name'].unique()) if 'category.name' in searcher.data.columns else []
        }
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("启动快速数据字段检索器Web应用...")
    print("访问 http://localhost:5000 开始使用")
    print("注意：此版本不使用翻译功能，启动更快")
    app.run(host='0.0.0.0', port=5000, debug=True)
