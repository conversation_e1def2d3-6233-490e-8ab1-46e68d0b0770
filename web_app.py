#!/usr/bin/env python3
"""
数据字段检索器 - Web应用
提供网页交互界面进行字段搜索
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import json
import time
from fixed_searcher import FixedFieldSearcher
from typing import Dict, List
import os

app = Flask(__name__)

# 全局搜索器实例
searcher = None

def init_searcher():
    """初始化搜索器"""
    global searcher
    if searcher is None:
        print("正在初始化搜索器...")
        searcher = FixedFieldSearcher("split_files")
        print("搜索器初始化完成")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/init', methods=['POST'])
def api_init():
    """初始化API"""
    try:
        init_searcher()
        
        # 获取基础信息
        stats = searcher.get_statistics()
        tables = searcher.get_tables()
        categories = searcher.get_categories()
        regions = searcher.get_regions()
        types = searcher.get_types()
        
        return jsonify({
            'success': True,
            'stats': stats,
            'tables': tables,
            'categories': categories,
            'regions': regions,
            'types': types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search', methods=['POST'])
def api_search():
    """搜索API"""
    try:
        # 确保搜索器已初始化
        if searcher is None:
            init_searcher()

        if searcher is None:
            return jsonify({
                'success': False,
                'error': '搜索器初始化失败'
            }), 500
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                'success': False,
                'error': '查询不能为空'
            }), 400
        
        # 搜索参数
        top_k = data.get('top_k', 10)
        similarity_method = data.get('similarity_method', 'hybrid')
        min_score = data.get('min_score', 0.01)  # 降低默认最小相似度阈值
        region_filter = data.get('region_filter')
        type_filter = data.get('type_filter')
        table_filter = data.get('table_filter')
        category_filter = data.get('category_filter')
        
        # 执行搜索
        start_time = time.time()
        results = searcher.search(
            query=query,
            top_k=top_k,
            similarity_method=similarity_method,
            min_score=min_score,
            region_filter=region_filter if region_filter else None,
            type_filter=type_filter if type_filter else None,
            table_filter=table_filter if table_filter else None,
            category_filter=category_filter if category_filter else None
        )
        search_time = time.time() - start_time
        
        # 转换结果为JSON格式
        results_json = []
        for result in results:
            results_json.append({
                'id': str(result.id),
                'description': str(result.description),
                'chinese_description': searcher.get_chinese_description(str(result.description)) if hasattr(searcher, 'get_chinese_description') else None,
                'similarity_score': float(result.similarity_score),
                'region': str(result.region),
                'universe': str(result.universe),
                'type': str(result.type),
                'category': str(result.category),
                'subcategory': str(result.subcategory),
                'dataset_name': str(result.dataset_name),
                'coverage': float(result.coverage) if result.coverage is not None else 0.0,
                'user_count': int(result.user_count) if result.user_count is not None else 0,
                'alpha_count': int(result.alpha_count) if result.alpha_count is not None else 0,
                'table_name': str(getattr(result, 'table_name', ''))
            })
        
        return jsonify({
            'success': True,
            'results': results_json,
            'search_time': search_time,
            'total_results': len(results),
            'query': query,
            'filters': {
                'region': region_filter,
                'type': type_filter,
                'table': table_filter,
                'category': category_filter
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/table/<table_name>')
def api_table_info(table_name):
    """获取表格详细信息"""
    try:
        if searcher is None:
            return jsonify({
                'success': False,
                'error': '搜索器未初始化'
            }), 400
        
        tables = searcher.get_tables()
        if table_name not in tables:
            return jsonify({
                'success': False,
                'error': f'表格 {table_name} 不存在'
            }), 404
        
        table_info = tables[table_name]
        
        # 获取表格中的示例数据
        if table_name in searcher.tables:
            sample_data = searcher.tables[table_name].head(5).to_dict('records')
        else:
            sample_data = []
        
        return jsonify({
            'success': True,
            'table_info': table_info,
            'sample_data': sample_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export', methods=['POST'])
def api_export():
    """导出搜索结果"""
    try:
        data = request.get_json()
        results = data.get('results', [])
        
        if not results:
            return jsonify({
                'success': False,
                'error': '没有可导出的结果'
            }), 400
        
        # 创建CSV内容
        import pandas as pd

        # 确保所有数据类型都是可序列化的
        cleaned_results = []
        for result in results:
            cleaned_result = {}
            for key, value in result.items():
                if pd.isna(value):
                    cleaned_result[key] = ''
                elif isinstance(value, (int, float)):
                    cleaned_result[key] = float(value) if isinstance(value, float) else int(value)
                else:
                    cleaned_result[key] = str(value)
            cleaned_results.append(cleaned_result)

        df = pd.DataFrame(cleaned_results)
        
        # 生成文件名
        timestamp = int(time.time())
        filename = f"search_results_{timestamp}.csv"
        filepath = os.path.join('exports', filename)
        
        # 确保导出目录存在
        os.makedirs('exports', exist_ok=True)
        
        # 保存文件
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        return jsonify({
            'success': True,
            'filename': filename,
            'download_url': f'/download/{filename}'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        return send_from_directory('exports', filename, as_attachment=True)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stats')
def api_stats():
    """获取统计信息"""
    try:
        if searcher is None:
            return jsonify({
                'success': False,
                'error': '搜索器未初始化'
            }), 400
        
        stats = searcher.get_statistics()
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('exports', exist_ok=True)
    
    print("启动数据字段检索器Web应用...")
    print("访问 http://localhost:5000 开始使用")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
