#!/usr/bin/env python3
"""
Elasticsearch部署和配置脚本
自动部署ES服务，创建优化的索引，配置中文分词
"""

import os
import time
import json
import subprocess
import requests
from pathlib import Path
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
import pandas as pd

class ElasticsearchSetup:
    """Elasticsearch部署和配置"""
    
    def __init__(self, 
                 es_host: str = "localhost",
                 es_port: int = 9200,
                 index_name: str = "bilingual_fields"):
        self.es_host = es_host
        self.es_port = es_port
        self.index_name = index_name
        self.es_url = f"http://{es_host}:{es_port}"
        self.es = None
    
    def deploy_elasticsearch(self):
        """部署Elasticsearch服务"""
        print("🚀 部署Elasticsearch服务...")
        
        # 检查Docker是否安装
        try:
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            print("✅ Docker已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Docker未安装，请先安装Docker")
            return False
        
        # 检查ES是否已运行
        if self._check_elasticsearch_running():
            print("✅ Elasticsearch已在运行")
            return True
        
        # 停止现有容器
        try:
            subprocess.run(["docker", "stop", "elasticsearch"], capture_output=True)
            subprocess.run(["docker", "rm", "elasticsearch"], capture_output=True)
        except:
            pass
        
        # 启动Elasticsearch容器
        docker_cmd = [
            "docker", "run", "-d",
            "--name", "elasticsearch",
            "-p", f"{self.es_port}:9200",
            "-p", "9300:9300",
            "-e", "discovery.type=single-node",
            "-e", "ES_JAVA_OPTS=-Xms2g -Xmx2g",  # 设置内存
            "-e", "xpack.security.enabled=false",
            "elasticsearch:8.11.0"
        ]
        
        try:
            result = subprocess.run(docker_cmd, check=True, capture_output=True, text=True)
            print("✅ Elasticsearch容器启动成功")
            
            # 等待ES启动
            print("等待Elasticsearch启动...")
            for i in range(60):  # 最多等待60秒
                if self._check_elasticsearch_running():
                    print("✅ Elasticsearch启动完成")
                    return True
                time.sleep(1)
                print(f"等待中... ({i+1}/60)")
            
            print("❌ Elasticsearch启动超时")
            return False
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Elasticsearch启动失败: {e}")
            return False
    
    def _check_elasticsearch_running(self) -> bool:
        """检查Elasticsearch是否运行"""
        try:
            response = requests.get(f"{self.es_url}/_cluster/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def connect_elasticsearch(self) -> bool:
        """连接Elasticsearch"""
        try:
            self.es = Elasticsearch([{
                'host': self.es_host,
                'port': self.es_port,
                'scheme': 'http'
            }])
            
            if self.es.ping():
                print("✅ Elasticsearch连接成功")
                return True
            else:
                print("❌ Elasticsearch连接失败")
                return False
                
        except Exception as e:
            print(f"❌ Elasticsearch连接错误: {e}")
            return False
    
    def install_chinese_analyzer(self):
        """安装中文分词插件"""
        print("📦 安装中文分词插件...")
        
        # 检查是否已安装ik插件
        try:
            response = requests.get(f"{self.es_url}/_cat/plugins", timeout=10)
            if "analysis-ik" in response.text:
                print("✅ IK中文分词插件已安装")
                return True
        except:
            pass
        
        # 在容器中安装IK插件
        install_cmd = [
            "docker", "exec", "elasticsearch",
            "elasticsearch-plugin", "install",
            "https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v8.11.0/elasticsearch-analysis-ik-8.11.0.zip"
        ]
        
        try:
            print("正在安装IK插件...")
            result = subprocess.run(install_cmd, check=True, capture_output=True, text=True)
            print("✅ IK插件安装成功")
            
            # 重启Elasticsearch
            print("重启Elasticsearch...")
            subprocess.run(["docker", "restart", "elasticsearch"], check=True)
            
            # 等待重启完成
            time.sleep(30)
            for i in range(30):
                if self._check_elasticsearch_running():
                    print("✅ Elasticsearch重启完成")
                    return True
                time.sleep(1)
            
            print("❌ Elasticsearch重启超时")
            return False
            
        except subprocess.CalledProcessError as e:
            print(f"❌ IK插件安装失败: {e}")
            # 即使插件安装失败，也可以使用标准分词器
            return True
    
    def create_optimized_index(self):
        """创建优化的索引"""
        print("🔧 创建优化索引...")
        
        if not self.es:
            print("❌ Elasticsearch未连接")
            return False
        
        # 删除现有索引
        if self.es.indices.exists(index=self.index_name):
            self.es.indices.delete(index=self.index_name)
            print(f"已删除现有索引: {self.index_name}")
        
        # 定义索引设置和映射
        index_config = {
            "settings": {
                "number_of_shards": 2,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "chinese_analyzer": {
                            "type": "custom",
                            "tokenizer": "ik_max_word",
                            "filter": ["lowercase", "stop"]
                        },
                        "english_analyzer": {
                            "type": "custom", 
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop", "snowball"]
                        },
                        "id_analyzer": {
                            "type": "custom",
                            "tokenizer": "keyword",
                            "filter": ["lowercase"]
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    "id": {
                        "type": "text",
                        "analyzer": "id_analyzer",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "ngram": {
                                "type": "text",
                                "analyzer": "standard"
                            }
                        }
                    },
                    "description": {
                        "type": "text",
                        "analyzer": "english_analyzer",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "raw": {"type": "text", "analyzer": "standard"}
                        }
                    },
                    "description_zh": {
                        "type": "text",
                        "analyzer": "chinese_analyzer",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "raw": {"type": "text", "analyzer": "standard"}
                        }
                    },
                    "region": {"type": "keyword"},
                    "universe": {"type": "keyword"},
                    "type": {"type": "keyword"},
                    "category": {"type": "keyword"},
                    "subcategory": {"type": "keyword"},
                    "dataset_name": {"type": "keyword"},
                    "table_name": {"type": "keyword"},
                    "coverage": {"type": "float"},
                    "user_count": {"type": "integer"},
                    "alpha_count": {"type": "integer"},
                    "quality_score": {"type": "float"}
                }
            }
        }
        
        # 如果IK插件不可用，使用标准分词器
        try:
            self.es.indices.create(index=self.index_name, body=index_config)
            print(f"✅ 索引创建成功: {self.index_name}")
            return True
        except Exception as e:
            if "ik_max_word" in str(e):
                print("⚠️  IK插件不可用，使用标准分词器")
                # 修改为标准分词器
                index_config["settings"]["analysis"]["analyzer"]["chinese_analyzer"]["tokenizer"] = "standard"
                try:
                    self.es.indices.create(index=self.index_name, body=index_config)
                    print(f"✅ 索引创建成功（标准分词器）: {self.index_name}")
                    return True
                except Exception as e2:
                    print(f"❌ 索引创建失败: {e2}")
                    return False
            else:
                print(f"❌ 索引创建失败: {e}")
                return False
    
    def test_analyzers(self):
        """测试分词器"""
        print("🧪 测试分词器...")
        
        if not self.es:
            return
        
        test_texts = [
            ("market capitalization", "english_analyzer"),
            ("市场资本化", "chinese_analyzer"),
            ("price_volume_ratio", "id_analyzer")
        ]
        
        for text, analyzer in test_texts:
            try:
                response = self.es.indices.analyze(
                    index=self.index_name,
                    body={
                        "analyzer": analyzer,
                        "text": text
                    }
                )
                
                tokens = [token['token'] for token in response['tokens']]
                print(f"  {analyzer}: '{text}' -> {tokens}")
                
            except Exception as e:
                print(f"  {analyzer} 测试失败: {e}")
    
    def setup_complete_system(self) -> bool:
        """完整系统设置"""
        print("🌟 开始Elasticsearch完整部署")
        print("=" * 50)
        
        # 1. 部署ES服务
        if not self.deploy_elasticsearch():
            return False
        
        # 2. 连接ES
        if not self.connect_elasticsearch():
            return False
        
        # 3. 安装中文分词插件
        self.install_chinese_analyzer()
        
        # 4. 重新连接（插件安装后）
        if not self.connect_elasticsearch():
            return False
        
        # 5. 创建优化索引
        if not self.create_optimized_index():
            return False
        
        # 6. 测试分词器
        self.test_analyzers()
        
        print("\n✅ Elasticsearch部署完成！")
        print(f"访问地址: {self.es_url}")
        print(f"索引名称: {self.index_name}")
        
        return True


def main():
    """主函数"""
    print("🔧 Elasticsearch自动部署脚本")
    print("=" * 50)
    
    # 创建设置器
    setup = ElasticsearchSetup()
    
    # 执行完整设置
    success = setup.setup_complete_system()
    
    if success:
        print("\n🎉 Elasticsearch部署成功！")
        print("现在可以开始索引数据了")
    else:
        print("\n❌ Elasticsearch部署失败")
        print("请检查Docker是否正常运行")


if __name__ == "__main__":
    main()
