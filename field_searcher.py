#!/usr/bin/env python3
"""
数据字段检索器 - 基于文本相似度的高效搜索系统
支持多种相似度算法和智能匹配策略
"""

import pandas as pd
import numpy as np
import re
import os
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import jieba
import jieba.analyse
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from difflib import SequenceMatcher
import warnings
warnings.filterwarnings('ignore')

@dataclass
class SearchResult:
    """搜索结果数据类"""
    id: str
    description: str
    similarity_score: float
    region: str
    universe: str
    type: str
    category: str
    subcategory: str
    dataset_name: str
    coverage: float
    user_count: int
    alpha_count: int

class FieldSearcher:
    """数据字段检索器主类"""
    
    def __init__(self, data_dir: str = "split_files"):
        """
        初始化搜索器
        
        Args:
            data_dir: 数据文件目录路径
        """
        self.data_dir = Path(data_dir)
        self.data = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        self.descriptions = None
        
        # 初始化jieba分词
        jieba.initialize()
        
        # 加载数据
        self._load_data()
        self._prepare_search_index()
    
    def _load_data(self):
        """加载所有CSV文件并保持表格分离"""
        print("正在加载数据文件...")

        self.tables = {}  # 存储各个表的数据
        self.table_metadata = {}  # 存储表的元数据
        all_data = []
        csv_files = list(self.data_dir.glob("*.csv"))

        if not csv_files:
            raise FileNotFoundError(f"在目录 {self.data_dir} 中未找到CSV文件")

        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                # 确保必要的列存在
                required_cols = ['id', 'description']
                if all(col in df.columns for col in required_cols):
                    table_name = file_path.stem  # 不包含扩展名的文件名

                    # 添加表名列
                    df['table_name'] = table_name

                    # 存储单独的表
                    self.tables[table_name] = df.copy()

                    # 存储表的元数据
                    self.table_metadata[table_name] = {
                        'file_path': str(file_path),
                        'record_count': len(df),
                        'regions': df['region'].unique().tolist() if 'region' in df.columns else [],
                        'types': df['type'].unique().tolist() if 'type' in df.columns else [],
                        'categories': df['category.name'].unique().tolist() if 'category.name' in df.columns else []
                    }

                    all_data.append(df)
                    print(f"已加载: {file_path.name} ({len(df)} 条记录)")
                else:
                    print(f"跳过文件 {file_path.name}: 缺少必要列")
            except Exception as e:
                print(f"加载文件 {file_path.name} 时出错: {e}")

        if not all_data:
            raise ValueError("没有成功加载任何数据文件")

        # 合并所有数据
        self.data = pd.concat(all_data, ignore_index=True)

        # 去重（基于id和description）
        self.data = self.data.drop_duplicates(subset=['id', 'description'])

        print(f"数据加载完成，共 {len(self.data)} 条唯一记录，来自 {len(self.tables)} 个表")
    
    def _prepare_search_index(self):
        """准备搜索索引"""
        print("正在构建搜索索引...")
        
        # 提取描述文本
        self.descriptions = self.data['description'].fillna('').astype(str).tolist()
        
        # 预处理文本：中英文混合处理
        processed_descriptions = []
        for desc in self.descriptions:
            # 英文部分：转小写，保留字母数字
            english_part = re.sub(r'[^a-zA-Z0-9\s]', ' ', desc.lower())
            
            # 中文部分：使用jieba分词
            chinese_part = ' '.join(jieba.cut(desc))
            
            # 合并处理结果
            processed = f"{english_part} {chinese_part}".strip()
            processed_descriptions.append(processed)
        
        # 构建TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=10000,
            ngram_range=(1, 2),  # 使用1-gram和2-gram
            min_df=1,
            max_df=0.95,
            stop_words=None  # 不使用停用词，保留所有信息
        )
        
        # 计算TF-IDF矩阵
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(processed_descriptions)
        
        print("搜索索引构建完成")
    
    def _preprocess_query(self, query: str) -> str:
        """预处理查询文本"""
        # 英文部分处理
        english_part = re.sub(r'[^a-zA-Z0-9\s]', ' ', query.lower())

        # 中文部分处理
        chinese_part = ' '.join(jieba.cut(query))

        # 添加同义词扩展
        expanded_query = self._expand_synonyms(query)

        return f"{english_part} {chinese_part} {expanded_query}".strip()

    def _expand_synonyms(self, query: str) -> str:
        """扩展同义词以提高搜索准确性"""
        # 金融术语同义词映射
        synonyms = {
            '价格': 'price cost value',
            '现金流': 'cash flow cashflow',
            '收入': 'revenue income earnings',
            '利润': 'profit earnings income',
            '资产': 'asset assets',
            '负债': 'liability debt',
            '股价': 'stock price share price',
            '市值': 'market cap capitalization',
            '成交量': 'volume trading volume',
            '波动': 'volatility variance',
            '风险': 'risk beta',
            '回报': 'return returns',
            '增长': 'growth',
            '比率': 'ratio rate',
            '指标': 'indicator metric',
            '财务': 'financial finance',
            '会计': 'accounting',
            '估值': 'valuation',
            '分析': 'analysis analytics'
        }

        expanded = []
        for chinese_term, english_terms in synonyms.items():
            if chinese_term in query:
                expanded.append(english_terms)

        return ' '.join(expanded)

    def _calculate_id_similarity(self, query: str) -> np.ndarray:
        """计算与ID的相似度"""
        query_lower = query.lower()
        similarities = []

        for _, row in self.data.iterrows():
            field_id = str(row['id']).lower()

            # 计算ID匹配度
            similarity = 0.0

            # 完全匹配
            if query_lower == field_id:
                similarity = 1.0
            # 包含匹配
            elif query_lower in field_id or field_id in query_lower:
                similarity = 0.8
            # 部分匹配（使用SequenceMatcher）
            else:
                from difflib import SequenceMatcher
                similarity = SequenceMatcher(None, query_lower, field_id).ratio()

            similarities.append(similarity)

        return np.array(similarities)
    
    def _calculate_cosine_similarity(self, query: str) -> np.ndarray:
        """计算余弦相似度"""
        processed_query = self._preprocess_query(query)
        query_vector = self.tfidf_vectorizer.transform([processed_query])
        similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
        return similarities
    
    def _calculate_fuzzy_similarity(self, query: str) -> np.ndarray:
        """计算模糊匹配相似度"""
        query_lower = query.lower()
        similarities = []
        
        for desc in self.descriptions:
            desc_lower = desc.lower()
            
            # 使用SequenceMatcher计算相似度
            similarity = SequenceMatcher(None, query_lower, desc_lower).ratio()
            
            # 如果查询是描述的子串，给予额外加分
            if query_lower in desc_lower:
                similarity += 0.3
            
            # 如果描述是查询的子串，也给予加分
            if desc_lower in query_lower:
                similarity += 0.2
            
            similarities.append(min(similarity, 1.0))  # 确保不超过1.0
        
        return np.array(similarities)
    
    def _calculate_keyword_similarity(self, query: str) -> np.ndarray:
        """计算关键词匹配相似度"""
        # 提取查询关键词
        query_keywords = set(jieba.cut(query.lower()))
        query_keywords.update(query.lower().split())
        
        similarities = []
        for desc in self.descriptions:
            desc_lower = desc.lower()
            desc_keywords = set(jieba.cut(desc_lower))
            desc_keywords.update(desc_lower.split())
            
            # 计算关键词交集
            intersection = query_keywords.intersection(desc_keywords)
            union = query_keywords.union(desc_keywords)
            
            # Jaccard相似度
            if len(union) > 0:
                similarity = len(intersection) / len(union)
            else:
                similarity = 0.0
            
            similarities.append(similarity)
        
        return np.array(similarities)
    
    def search(self,
               query: str,
               top_k: int = 10,
               similarity_method: str = 'hybrid',
               min_score: float = 0.1,
               region_filter: Optional[str] = None,
               type_filter: Optional[str] = None,
               table_filter: Optional[str] = None,
               category_filter: Optional[str] = None) -> List[SearchResult]:
        """
        执行搜索

        Args:
            query: 搜索查询
            top_k: 返回结果数量
            similarity_method: 相似度计算方法 ('cosine', 'fuzzy', 'keyword', 'hybrid')
            min_score: 最小相似度阈值
            region_filter: 地区过滤器
            type_filter: 类型过滤器
            table_filter: 表格过滤器
            category_filter: 分类过滤器

        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []
        
        print(f"搜索查询: '{query}' (方法: {similarity_method})")
        
        # 计算相似度
        if similarity_method == 'cosine':
            similarities = self._calculate_cosine_similarity(query)
        elif similarity_method == 'fuzzy':
            similarities = self._calculate_fuzzy_similarity(query)
        elif similarity_method == 'keyword':
            similarities = self._calculate_keyword_similarity(query)
        elif similarity_method == 'hybrid':
            # 混合方法：结合多种相似度
            cosine_sim = self._calculate_cosine_similarity(query)
            fuzzy_sim = self._calculate_fuzzy_similarity(query)
            keyword_sim = self._calculate_keyword_similarity(query)

            # 加权组合
            similarities = (0.5 * cosine_sim + 0.3 * fuzzy_sim + 0.2 * keyword_sim)
        else:
            raise ValueError(f"不支持的相似度方法: {similarity_method}")

        # 计算ID相似度用于打破平局
        id_similarities = self._calculate_id_similarity(query)

        # 应用过滤器
        mask = similarities >= min_score
        if region_filter:
            mask &= (self.data['region'] == region_filter)
        if type_filter:
            mask &= (self.data['type'] == type_filter)
        if table_filter:
            mask &= (self.data['table_name'] == table_filter)
        if category_filter:
            mask &= (self.data['category.name'] == category_filter)
        
        # 获取有效索引
        valid_indices = np.where(mask)[0]
        valid_similarities = similarities[mask]
        valid_id_similarities = id_similarities[mask]

        # 创建复合排序键：先按description相似度，再按ID相似度
        # 使用负值因为argsort是升序的
        sort_keys = np.column_stack((-valid_similarities, -valid_id_similarities))

        # 按复合键排序
        sorted_indices = np.lexsort((sort_keys[:, 1], sort_keys[:, 0]))[::-1][:top_k]
        
        # 构建结果
        results = []
        for idx in sorted_indices:
            original_idx = valid_indices[idx]
            row = self.data.iloc[original_idx]
            
            result = SearchResult(
                id=row['id'],
                description=row['description'],
                similarity_score=valid_similarities[idx],
                region=row.get('region', ''),
                universe=row.get('universe', ''),
                type=row.get('type', ''),
                category=row.get('category.name', ''),
                subcategory=row.get('subcategory.name', ''),
                dataset_name=row.get('dataset.name', ''),
                coverage=row.get('coverage', 0.0),
                user_count=row.get('userCount', 0),
                alpha_count=row.get('alphaCount', 0)
            )
            # 添加table_name属性
            result.table_name = row.get('table_name', '')
            results.append(result)
        
        print(f"找到 {len(results)} 个匹配结果")
        return results
    
    def print_results(self, results: List[SearchResult], show_details: bool = True):
        """打印搜索结果"""
        if not results:
            print("未找到匹配结果")
            return
        
        print(f"\n{'='*80}")
        print(f"搜索结果 (共 {len(results)} 条)")
        print(f"{'='*80}")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. ID: {result.id}")
            print(f"   描述: {result.description}")
            print(f"   相似度: {result.similarity_score:.4f}")
            
            if show_details:
                print(f"   地区: {result.region} | 类型: {result.type}")
                print(f"   分类: {result.category} > {result.subcategory}")
                print(f"   数据集: {result.dataset_name}")
                print(f"   覆盖率: {result.coverage:.4f} | 用户数: {result.user_count} | Alpha数: {result.alpha_count}")
    
    def get_statistics(self) -> Dict:
        """获取数据统计信息"""
        stats = {
            'total_records': len(self.data),
            'unique_regions': self.data['region'].nunique(),
            'unique_types': self.data['type'].nunique(),
            'unique_categories': self.data['category.name'].nunique(),
            'unique_tables': len(self.tables),
            'regions': self.data['region'].value_counts().to_dict(),
            'types': self.data['type'].value_counts().to_dict(),
            'categories': self.data['category.name'].value_counts().head(10).to_dict(),
            'tables': {name: meta['record_count'] for name, meta in self.table_metadata.items()}
        }
        return stats

    def get_tables(self) -> Dict[str, Dict]:
        """获取所有表格信息"""
        return self.table_metadata

    def get_categories(self) -> List[str]:
        """获取所有分类"""
        return sorted(self.data['category.name'].dropna().unique().tolist())

    def get_regions(self) -> List[str]:
        """获取所有地区"""
        return sorted(self.data['region'].dropna().unique().tolist())

    def get_types(self) -> List[str]:
        """获取所有类型"""
        return sorted(self.data['type'].dropna().unique().tolist())

    def search_by_table(self,
                       query: str,
                       table_name: str,
                       top_k: int = 10,
                       similarity_method: str = 'hybrid',
                       min_score: float = 0.1) -> List[SearchResult]:
        """
        在指定表中搜索

        Args:
            query: 搜索查询
            table_name: 表名
            top_k: 返回结果数量
            similarity_method: 相似度计算方法
            min_score: 最小相似度阈值

        Returns:
            搜索结果列表
        """
        return self.search(
            query=query,
            top_k=top_k,
            similarity_method=similarity_method,
            min_score=min_score,
            table_filter=table_name
        )


def main():
    """主函数 - 演示搜索器功能"""
    try:
        # 初始化搜索器
        searcher = FieldSearcher()
        
        # 显示统计信息
        stats = searcher.get_statistics()
        print(f"\n数据统计:")
        print(f"总记录数: {stats['total_records']}")
        print(f"地区数: {stats['unique_regions']}")
        print(f"类型数: {stats['unique_types']}")
        print(f"分类数: {stats['unique_categories']}")
        
        # 示例搜索
        test_queries = [
            "market capitalization",
            "价格",
            "volume",
            "资产",
            "revenue",
            "现金流"
        ]
        
        for query in test_queries:
            print(f"\n{'='*60}")
            results = searcher.search(query, top_k=5, similarity_method='hybrid')
            searcher.print_results(results, show_details=False)
        
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
