#!/usr/bin/env python3
"""
测试增强搜索功能
包括翻译和开源模糊搜索
"""

from fixed_searcher import FixedFieldSearcher
import time

def test_translation_and_fuzzy_search():
    """测试翻译和模糊搜索功能"""
    print("🚀 测试增强搜索功能")
    print("=" * 60)
    
    # 初始化搜索器（启用翻译）
    print("正在初始化增强搜索器...")
    searcher = FixedFieldSearcher(enable_translation=True)
    
    # 测试用例
    test_cases = [
        # 英文测试
        {"query": "market", "method": "smart", "description": "英文智能搜索"},
        {"query": "market", "method": "fuzzy_search", "description": "英文模糊搜索"},
        {"query": "market", "method": "id_priority", "description": "英文ID优先"},
        
        # 中文测试
        {"query": "市场", "method": "smart", "description": "中文智能搜索"},
        {"query": "市场", "method": "chinese_fuzzy", "description": "中文模糊搜索"},
        {"query": "价格", "method": "smart", "description": "中文价格搜索"},
        {"query": "成交量", "method": "smart", "description": "中文成交量搜索"},
        
        # 模糊匹配测试
        {"query": "mkt", "method": "fuzzy_search", "description": "缩写模糊搜索"},
        {"query": "capitaliztion", "method": "fuzzy_search", "description": "拼写错误模糊搜索"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   查询: '{test_case['query']}' (方法: {test_case['method']})")
        
        start_time = time.time()
        try:
            results = searcher.search(
                test_case['query'], 
                top_k=3, 
                similarity_method=test_case['method'],
                min_score=0.01
            )
            
            search_time = time.time() - start_time
            
            if results:
                print(f"   ✅ 找到 {len(results)} 个结果 (耗时: {search_time:.3f}秒)")
                for j, result in enumerate(results, 1):
                    score = result.similarity_score
                    chinese_desc = searcher.get_chinese_description(result.description)
                    
                    print(f"      {j}. [{score:.4f}] {result.id}")
                    print(f"         英文: {result.description[:50]}...")
                    if chinese_desc != result.description:
                        print(f"         中文: {chinese_desc[:50]}...")
            else:
                print(f"   ❌ 未找到结果 (耗时: {search_time:.3f}秒)")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")

def test_translation_quality():
    """测试翻译质量"""
    print(f"\n\n🌐 测试翻译质量")
    print("=" * 60)
    
    searcher = FixedFieldSearcher(enable_translation=True)
    
    # 测试一些典型的金融术语翻译
    test_descriptions = [
        "Market capitalization",
        "Daily volume",
        "Price to earnings ratio",
        "Return on equity",
        "Dividend yield",
        "Beta coefficient",
        "Cash flow",
        "Book value per share"
    ]
    
    print("金融术语翻译测试:")
    for desc in test_descriptions:
        chinese = searcher.get_chinese_description(desc)
        print(f"  {desc:<25} -> {chinese}")

def test_search_methods_comparison():
    """对比不同搜索方法的效果"""
    print(f"\n\n📊 搜索方法对比")
    print("=" * 60)
    
    searcher = FixedFieldSearcher(enable_translation=True)
    
    test_queries = ["market", "价格", "mkt", "capitaliztion"]
    methods = [
        ("smart", "智能匹配"),
        ("id_priority", "ID优先"),
        ("fuzzy_search", "模糊搜索"),
        ("chinese_fuzzy", "中文模糊")
    ]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        print("-" * 40)
        
        for method_code, method_name in methods:
            try:
                start_time = time.time()
                results = searcher.search(query, top_k=1, similarity_method=method_code, min_score=0.01)
                search_time = time.time() - start_time
                
                if results:
                    best = results[0]
                    print(f"  {method_name:<12}: [{best.similarity_score:.4f}] {best.id} ({search_time:.3f}s)")
                else:
                    print(f"  {method_name:<12}: 无结果 ({search_time:.3f}s)")
            except Exception as e:
                print(f"  {method_name:<12}: 错误 - {e}")

def main():
    """主测试函数"""
    try:
        test_translation_and_fuzzy_search()
        test_translation_quality()
        test_search_methods_comparison()
        
        print(f"\n\n🎉 测试完成！")
        print("=" * 60)
        print("✅ 翻译功能已集成")
        print("✅ 开源模糊搜索已集成")
        print("✅ 智能搜索方法已添加")
        print("✅ 中文搜索支持已增强")
        
        print(f"\n💡 使用建议:")
        print("- 使用 'smart' 方法获得最佳搜索体验")
        print("- 中文查询会自动使用中文模糊搜索")
        print("- 英文短查询使用ID优先匹配")
        print("- 英文长查询使用模糊搜索")
        print("- Web界面已更新，支持所有新功能")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
