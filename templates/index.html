<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字段检索器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .search-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .result-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .similarity-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .loading {
            display: none;
        }
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
        }
        .table-badge {
            background-color: #6c757d;
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 头部搜索区域 -->
        <div class="search-container">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <h1 class="mb-4">
                            <i class="bi bi-search"></i>
                            数据字段检索器
                        </h1>
                        <p class="lead">智能搜索金融数据字段，支持中英文混合查询</p>
                    </div>
                </div>
                
                <!-- 搜索表单 -->
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body">
                                <form id="searchForm">
                                    <div class="input-group mb-3">
                                        <input type="text" class="form-control form-control-lg" 
                                               id="queryInput" placeholder="输入搜索关键词，如：market cap, 价格, cash flow..."
                                               autocomplete="off">
                                        <button class="btn btn-primary btn-lg" type="submit">
                                            <i class="bi bi-search"></i> 搜索
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="container mt-4">
            <!-- 初始化状态 -->
            <div id="initSection" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">正在初始化...</span>
                </div>
                <p class="mt-2">正在初始化搜索引擎，请稍候...</p>
            </div>

            <!-- 过滤器和统计信息 -->
            <div id="mainContent" style="display: none;">
                <div class="row">
                    <!-- 左侧过滤器 -->
                    <div class="col-md-3">
                        <div class="filter-section">
                            <h5><i class="bi bi-funnel"></i> 搜索过滤器</h5>
                            
                            <!-- 相似度方法 -->
                            <div class="mb-3">
                                <label class="form-label">相似度算法</label>
                                <select class="form-select" id="similarityMethod">
                                    <option value="semantic_enhanced">语义增强 (推荐)</option>
                                    <option value="enhanced">增强方法</option>
                                    <option value="simple">简单匹配</option>
                                    <option value="cosine">余弦相似度</option>
                                </select>
                            </div>

                            <!-- 结果数量 -->
                            <div class="mb-3">
                                <label class="form-label">结果数量</label>
                                <select class="form-select" id="topK">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>

                            <!-- 最小相似度 -->
                            <div class="mb-3">
                                <label class="form-label">最小相似度</label>
                                <input type="range" class="form-range" id="minScore"
                                       min="0" max="1" step="0.01" value="0.01">
                                <small class="text-muted">当前值: <span id="minScoreValue">0.01</span></small>
                            </div>

                            <!-- 表格过滤 -->
                            <div class="mb-3">
                                <label class="form-label">数据表</label>
                                <select class="form-select" id="tableFilter">
                                    <option value="">所有表格</option>
                                </select>
                            </div>

                            <!-- 地区过滤 -->
                            <div class="mb-3">
                                <label class="form-label">地区</label>
                                <select class="form-select" id="regionFilter">
                                    <option value="">所有地区</option>
                                </select>
                            </div>

                            <!-- 类型过滤 -->
                            <div class="mb-3">
                                <label class="form-label">数据类型</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="">所有类型</option>
                                </select>
                            </div>

                            <!-- 分类过滤 -->
                            <div class="mb-3">
                                <label class="form-label">分类</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">所有分类</option>
                                </select>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div class="card stats-card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-bar-chart"></i> 数据统计
                                </h6>
                                <div id="statsContent">
                                    <p class="mb-1">总记录数: <span id="totalRecords">-</span></p>
                                    <p class="mb-1">数据表数: <span id="totalTables">-</span></p>
                                    <p class="mb-1">地区数: <span id="totalRegions">-</span></p>
                                    <p class="mb-0">分类数: <span id="totalCategories">-</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧搜索结果 -->
                    <div class="col-md-9">
                        <!-- 搜索状态 -->
                        <div id="searchStatus" class="alert alert-info" style="display: none;">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                正在搜索...
                            </div>
                        </div>

                        <!-- 搜索结果头部 -->
                        <div id="resultsHeader" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 id="resultsTitle">搜索结果</h5>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm" id="exportBtn">
                                        <i class="bi bi-download"></i> 导出结果
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索结果列表 -->
                        <div id="searchResults"></div>

                        <!-- 无结果提示 -->
                        <div id="noResults" class="text-center py-5" style="display: none;">
                            <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-3">未找到匹配结果</h5>
                            <p class="text-muted">请尝试调整搜索关键词或过滤条件</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        let currentResults = [];
        let isInitialized = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
        });

        // 初始化应用
        async function initializeApp() {
            try {
                const response = await fetch('/api/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    populateFilters(data);
                    updateStats(data.stats);

                    // 显示主内容，隐藏初始化状态
                    document.getElementById('initSection').style.display = 'none';
                    document.getElementById('mainContent').style.display = 'block';
                    isInitialized = true;
                } else {
                    showError('初始化失败: ' + data.error);
                    isInitialized = false;
                }
            } catch (error) {
                showError('初始化失败: ' + error.message);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索表单提交
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                performSearch();
            });

            // 最小相似度滑块
            document.getElementById('minScore').addEventListener('input', function(e) {
                document.getElementById('minScoreValue').textContent = e.target.value;
            });

            // 导出按钮
            document.getElementById('exportBtn').addEventListener('click', exportResults);
        }

        // 填充过滤器选项
        function populateFilters(data) {
            // 填充表格选项
            const tableSelect = document.getElementById('tableFilter');
            Object.keys(data.tables).forEach(tableName => {
                const option = document.createElement('option');
                option.value = tableName;
                option.textContent = `${tableName} (${data.tables[tableName].record_count}条)`;
                tableSelect.appendChild(option);
            });

            // 填充地区选项
            const regionSelect = document.getElementById('regionFilter');
            data.regions.forEach(region => {
                const option = document.createElement('option');
                option.value = region;
                option.textContent = region;
                regionSelect.appendChild(option);
            });

            // 填充类型选项
            const typeSelect = document.getElementById('typeFilter');
            data.types.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                typeSelect.appendChild(option);
            });

            // 填充分类选项
            const categorySelect = document.getElementById('categoryFilter');
            data.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        }

        // 更新统计信息
        function updateStats(stats) {
            document.getElementById('totalRecords').textContent = stats.total_records.toLocaleString();
            document.getElementById('totalTables').textContent = stats.unique_tables;
            document.getElementById('totalRegions').textContent = stats.unique_regions;
            document.getElementById('totalCategories').textContent = stats.unique_categories;
        }

        // 执行搜索
        async function performSearch() {
            const query = document.getElementById('queryInput').value.trim();
            if (!query) {
                showError('请输入搜索关键词');
                return;
            }

            // 检查是否已初始化
            if (!isInitialized) {
                showError('系统正在初始化，请稍候再试');
                return;
            }

            // 显示搜索状态
            document.getElementById('searchStatus').style.display = 'block';
            document.getElementById('resultsHeader').style.display = 'none';
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('noResults').style.display = 'none';

            try {
                const searchParams = {
                    query: query,
                    top_k: parseInt(document.getElementById('topK').value),
                    similarity_method: document.getElementById('similarityMethod').value,
                    min_score: parseFloat(document.getElementById('minScore').value),
                    region_filter: document.getElementById('regionFilter').value || null,
                    type_filter: document.getElementById('typeFilter').value || null,
                    table_filter: document.getElementById('tableFilter').value || null,
                    category_filter: document.getElementById('categoryFilter').value || null
                };

                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(searchParams)
                });

                const data = await response.json();

                // 隐藏搜索状态
                document.getElementById('searchStatus').style.display = 'none';

                if (data.success) {
                    currentResults = data.results;
                    displayResults(data);
                } else {
                    showError('搜索失败: ' + data.error);
                }
            } catch (error) {
                document.getElementById('searchStatus').style.display = 'none';
                showError('搜索失败: ' + error.message);
            }
        }

        // 显示搜索结果
        function displayResults(data) {
            const resultsContainer = document.getElementById('searchResults');
            
            if (data.results.length === 0) {
                document.getElementById('noResults').style.display = 'block';
                return;
            }

            // 显示结果头部
            document.getElementById('resultsHeader').style.display = 'block';
            document.getElementById('resultsTitle').textContent = 
                `搜索结果 (${data.results.length}条，耗时${data.search_time.toFixed(3)}秒)`;

            // 生成结果HTML
            let html = '';
            data.results.forEach((result, index) => {
                const similarityColor = result.similarity_score >= 0.8 ? 'success' : 
                                      result.similarity_score >= 0.5 ? 'warning' : 'secondary';
                
                html += `
                    <div class="card result-card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">
                                    <code>${result.id}</code>
                                </h6>
                                <span class="badge bg-${similarityColor} similarity-badge">
                                    ${(result.similarity_score * 100).toFixed(1)}%
                                </span>
                            </div>
                            
                            <p class="card-text">${result.description}</p>
                            
                            <div class="row text-muted small">
                                <div class="col-md-6">
                                    <div><strong>地区:</strong> ${result.region}</div>
                                    <div><strong>类型:</strong> ${result.type}</div>
                                    <div><strong>分类:</strong> ${result.category}</div>
                                </div>
                                <div class="col-md-6">
                                    <div><strong>数据集:</strong> ${result.dataset_name}</div>
                                    <div><strong>覆盖率:</strong> ${(result.coverage * 100).toFixed(1)}%</div>
                                    <div><strong>用户数:</strong> ${result.user_count}</div>
                                </div>
                            </div>
                            
                            ${result.table_name ? `<div class="mt-2"><span class="table-badge">${result.table_name}</span></div>` : ''}
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;
        }

        // 导出结果
        async function exportResults() {
            if (currentResults.length === 0) {
                showError('没有可导出的结果');
                return;
            }

            try {
                const response = await fetch('/api/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        results: currentResults
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    showSuccess('结果导出成功');
                } else {
                    showError('导出失败: ' + data.error);
                }
            } catch (error) {
                showError('导出失败: ' + error.message);
            }
        }

        // 显示错误消息
        function showError(message) {
            alert('错误: ' + message);
        }

        // 显示成功消息
        function showSuccess(message) {
            alert('成功: ' + message);
        }
    </script>
</body>
</html>
