<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整版双语数据字段检索器 | Complete Bilingual Field Searcher</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        
        .result-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .match-type-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        
        .user-menu {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .bilingual-text {
            border-left: 3px solid #667eea;
            padding-left: 1rem;
            margin: 0.5rem 0;
        }
        
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .search-input {
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            border: 2px solid #e9ecef;
            font-size: 1.1rem;
        }
        
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-search {
            border-radius: 25px;
            padding: 0.75rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            font-weight: 600;
        }
        
        .advanced-options {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .feature-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin: 0.25rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .feature-enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .feature-disabled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .match-details {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .login-required {
            text-align: center;
            padding: 3rem;
        }
        
        .feature-highlight {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        
        .system-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- 用户菜单 -->
    <div class="user-menu">
        <div class="dropdown">
            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown">
                <i class="fas fa-user me-2"></i>
                <span id="usernameDisplay">未登录</span>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
            </ul>
        </div>
    </div>

    <!-- 头部 -->
    <div class="gradient-bg py-5">
        <div class="container text-center">
            <h1 class="display-4 mb-3">
                <i class="fas fa-rocket me-3"></i>
                完整版双语数据字段检索器
            </h1>
            <p class="lead mb-4">
                <span class="feature-highlight">🧠 语义搜索</span> | 
                <span class="feature-highlight">🔍 模糊匹配</span> | 
                <span class="feature-highlight">📚 同义词扩展</span> | 
                <span class="feature-highlight">🔤 拼写纠错</span> | 
                <span class="feature-highlight">🌐 双语支持</span>
            </p>
            
            <!-- 系统信息 -->
            <div class="system-info">
                <div class="row">
                    <div class="col-md-3">
                        <i class="fas fa-database fa-2x mb-2"></i>
                        <div>数据规模</div>
                        <small>420,786+ 条记录</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-language fa-2x mb-2"></i>
                        <div>双语支持</div>
                        <small>中英文智能搜索</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-brain fa-2x mb-2"></i>
                        <div>AI算法</div>
                        <small>语义理解 + 模糊匹配</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-bolt fa-2x mb-2"></i>
                        <div>搜索速度</div>
                        <small>毫秒级响应</small>
                    </div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="row mt-4" id="statsContainer" style="display: none;">
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 id="totalRecords">-</h3>
                        <small>总记录数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 id="bilingualRecords">-</h3>
                        <small>双语记录</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 id="uniqueTables">-</h3>
                        <small>数据表数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <h3 id="searchTime">-</h3>
                        <small>搜索耗时</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 登录提示 -->
        <div class="login-required" id="loginRequired">
            <i class="fas fa-lock fa-3x text-muted mb-3"></i>
            <h3>需要登录使用完整版功能</h3>
            <p class="text-muted">登录后可使用高级语义搜索、模糊匹配、同义词扩展等完整功能</p>
            <a href="/login" class="btn btn-primary me-2">
                <i class="fas fa-sign-in-alt me-2"></i>登录
            </a>
            <a href="/register" class="btn btn-outline-primary">
                <i class="fas fa-user-plus me-2"></i>注册
            </a>
        </div>

        <!-- 搜索容器 -->
        <div class="search-container" id="searchContainer" style="display: none;">
            <!-- 搜索栏 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" 
                               class="form-control search-input" 
                               id="searchInput" 
                               placeholder="输入中文或英文关键词，体验完整版高级搜索..."
                               onkeypress="handleKeyPress(event)">
                        <button class="btn btn-search" onclick="performSearch()">
                            <i class="fas fa-rocket me-2"></i>完整版搜索
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-success" onclick="exportResults()" id="exportBtn" disabled>
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                        <button class="btn btn-outline-info" onclick="showSystemInfo()">
                            <i class="fas fa-info-circle me-1"></i>系统信息
                        </button>
                    </div>
                </div>
            </div>

            <!-- 高级选项 -->
            <div class="advanced-options" id="advancedOptions" style="display: none;">
                <h6 class="mb-3">
                    <i class="fas fa-cogs me-2"></i>完整版高级搜索选项
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">结果数量</label>
                        <select class="form-select" id="topK">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">语言检测</label>
                        <select class="form-select" id="searchLanguage">
                            <option value="auto">智能检测</option>
                            <option value="zh">中文</option>
                            <option value="en">英文</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">模糊匹配阈值</label>
                        <select class="form-select" id="fuzzyThreshold">
                            <option value="0.5">0.5 (宽松)</option>
                            <option value="0.6" selected>0.6 (标准)</option>
                            <option value="0.7">0.7 (严格)</option>
                            <option value="0.8">0.8 (很严格)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">高级功能</label>
                        <div class="mt-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableSynonyms" checked>
                                <label class="form-check-label" for="enableSynonyms">📚 同义词扩展</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableSpellCheck" checked>
                                <label class="form-check-label" for="enableSpellCheck">🔤 拼写纠错</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableSemantic" checked>
                                <label class="form-check-label" for="enableSemantic">🧠 语义搜索</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableFuzzy" checked>
                                <label class="form-check-label" for="enableFuzzy">🔍 模糊匹配</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 选项切换按钮 -->
            <div class="text-center mb-3">
                <button class="btn btn-outline-secondary btn-sm" onclick="toggleAdvancedOptions()">
                    <i class="fas fa-cog me-1"></i>
                    <span id="optionsToggleText">显示高级选项</span>
                </button>
            </div>

            <!-- 搜索功能状态 -->
            <div id="searchFeatures" class="mb-3" style="display: none;">
                <small class="text-muted">启用功能: </small>
                <span id="featuresDisplay"></span>
            </div>

            <!-- 加载动画 -->
            <div class="text-center loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">正在执行完整版高级搜索...</p>
            </div>

            <!-- 搜索结果 -->
            <div id="searchResults"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentResults = [];
        let isLoggedIn = false;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        function checkLoginStatus() {
            fetch('/api/stats')
                .then(response => {
                    if (response.status === 401) {
                        showLoginRequired();
                    } else {
                        return response.json();
                    }
                })
                .then(data => {
                    if (data && data.success) {
                        showSearchInterface();
                        initializeApp();
                    }
                })
                .catch(error => {
                    showLoginRequired();
                });
        }

        function showLoginRequired() {
            document.getElementById('loginRequired').style.display = 'block';
            document.getElementById('searchContainer').style.display = 'none';
            document.getElementById('usernameDisplay').textContent = '未登录';
            isLoggedIn = false;
        }

        function showSearchInterface() {
            document.getElementById('loginRequired').style.display = 'none';
            document.getElementById('searchContainer').style.display = 'block';
            isLoggedIn = true;
        }

        async function initializeApp() {
            try {
                const response = await fetch('/api/init', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStats(data.stats);
                    document.getElementById('statsContainer').style.display = 'block';
                } else {
                    showAlert('初始化失败: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('初始化错误: ' + error.message, 'danger');
            }
        }

        function updateStats(stats) {
            document.getElementById('totalRecords').textContent = stats.total_records.toLocaleString();
            document.getElementById('bilingualRecords').textContent = stats.bilingual_records.toLocaleString();
            document.getElementById('uniqueTables').textContent = stats.unique_tables;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        async function performSearch() {
            if (!isLoggedIn) {
                showAlert('请先登录', 'warning');
                return;
            }

            const query = document.getElementById('searchInput').value.trim();
            
            if (!query) {
                showAlert('请输入搜索关键词', 'warning');
                return;
            }

            // 显示加载动画
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('searchResults').innerHTML = '';

            try {
                const searchData = {
                    query: query,
                    top_k: parseInt(document.getElementById('topK').value),
                    language: document.getElementById('searchLanguage').value,
                    enable_synonyms: document.getElementById('enableSynonyms').checked,
                    enable_spell_check: document.getElementById('enableSpellCheck').checked,
                    enable_semantic: document.getElementById('enableSemantic').checked,
                    enable_fuzzy: document.getElementById('enableFuzzy').checked,
                    fuzzy_threshold: parseFloat(document.getElementById('fuzzyThreshold').value)
                };

                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(searchData)
                });

                const data = await response.json();

                if (data.success) {
                    currentResults = data.results;
                    displayResults(data.results, data.search_time, data.detected_language, data.search_features);
                    document.getElementById('exportBtn').disabled = data.results.length === 0;
                    
                    // 更新搜索时间
                    document.getElementById('searchTime').textContent = (data.search_time * 1000).toFixed(0) + 'ms';
                    
                    // 显示搜索功能状态
                    displaySearchFeatures(data.search_features);
                } else {
                    if (response.status === 401) {
                        showLoginRequired();
                    } else {
                        showAlert('搜索失败: ' + data.error, 'danger');
                    }
                }
            } catch (error) {
                showAlert('搜索错误: ' + error.message, 'danger');
            } finally {
                document.getElementById('loadingSpinner').style.display = 'none';
            }
        }

        function displaySearchFeatures(features) {
            const featuresContainer = document.getElementById('searchFeatures');
            const featuresDisplay = document.getElementById('featuresDisplay');
            
            let html = '';
            
            if (features.synonyms) html += '<span class="feature-badge feature-enabled">📚 同义词</span>';
            if (features.spell_check) html += '<span class="feature-badge feature-enabled">🔤 拼写纠错</span>';
            if (features.semantic) html += '<span class="feature-badge feature-enabled">🧠 语义搜索</span>';
            if (features.fuzzy) html += '<span class="feature-badge feature-enabled">🔍 模糊匹配</span>';
            
            featuresDisplay.innerHTML = html;
            featuresContainer.style.display = 'block';
        }

        function displayResults(results, searchTime, detectedLanguage, features) {
            const container = document.getElementById('searchResults');
            
            if (results.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">未找到匹配结果</h5>
                        <p class="text-muted">请尝试其他关键词或调整搜索选项</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>
                        <i class="fas fa-rocket me-2"></i>
                        完整版搜索结果 (${results.length} 条，耗时 ${(searchTime * 1000).toFixed(0)}ms)
                        <span class="badge bg-info ms-2">检测语言: ${detectedLanguage === 'zh' ? '中文' : '英文'}</span>
                    </h5>
                </div>
            `;

            results.forEach((result, index) => {
                const matchTypeInfo = getMatchTypeInfo(result.match_type);
                
                html += `
                    <div class="result-card card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">
                                    <span class="badge bg-primary me-2">${index + 1}</span>
                                    <code>${result.id}</code>
                                    <span class="badge ${matchTypeInfo.class} match-type-badge ms-2">
                                        ${matchTypeInfo.icon} ${matchTypeInfo.text}
                                    </span>
                                </h6>
                                <span class="badge bg-success">${result.similarity_score.toFixed(4)}</span>
                            </div>
                            
                            <div class="bilingual-text">
                                <div class="mb-2">
                                    <strong>🇺🇸 English:</strong> ${result.description}
                                </div>
                                <div>
                                    <strong>🇨🇳 中文:</strong> ${result.description_zh}
                                </div>
                            </div>
                            
                            ${result.match_details && Object.keys(result.match_details).length > 0 ? `
                                <div class="match-details">
                                    <strong>匹配详情:</strong> ${JSON.stringify(result.match_details)}
                                </div>
                            ` : ''}
                            
                            <div class="row mt-3 text-muted small">
                                <div class="col-md-3">
                                    <i class="fas fa-table me-1"></i> ${result.table_name}
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-globe me-1"></i> ${result.region}
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-tag me-1"></i> ${result.type}
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-users me-1"></i> ${result.user_count} users
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function getMatchTypeInfo(matchType) {
            const types = {
                'id_exact': { icon: '🎯', text: 'ID完全匹配', class: 'bg-success' },
                'id_partial': { icon: '🔍', text: 'ID部分匹配', class: 'bg-info' },
                'semantic': { icon: '🧠', text: '语义匹配', class: 'bg-primary' },
                'fuzzy_id': { icon: '🌀', text: 'ID模糊匹配', class: 'bg-warning' },
                'fuzzy_desc': { icon: '📝', text: '描述模糊匹配', class: 'bg-secondary' }
            };
            
            return types[matchType] || { icon: '❓', text: '未知匹配', class: 'bg-light' };
        }

        async function exportResults() {
            if (currentResults.length === 0) {
                showAlert('没有结果可导出', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ results: currentResults })
                });

                const data = await response.json();

                if (data.success) {
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    showAlert('导出成功！', 'success');
                } else {
                    showAlert('导出失败: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('导出错误: ' + error.message, 'danger');
            }
        }

        function toggleAdvancedOptions() {
            const options = document.getElementById('advancedOptions');
            const toggleText = document.getElementById('optionsToggleText');
            const isVisible = options.style.display !== 'none';
            
            options.style.display = isVisible ? 'none' : 'block';
            toggleText.textContent = isVisible ? '显示高级选项' : '隐藏高级选项';
        }

        function showSystemInfo() {
            showAlert('完整版系统已加载：🧠 语义搜索 | 🔍 模糊匹配 | 📚 同义词扩展 | 🔤 拼写纠错', 'info');
        }

        async function logout() {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('退出登录成功', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showAlert('退出登录失败: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('退出登录错误: ' + error.message, 'danger');
            }
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.search-container') || document.querySelector('.login-required');
            container.insertBefore(alertDiv, container.firstChild);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
