#!/usr/bin/env python3
"""
开源翻译工具
使用Google Translate API和本地翻译模型将英文字段描述翻译成中文
"""

import pandas as pd
import numpy as np
import json
import time
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 翻译相关导入
from googletrans import Translator
from transformers import pipeline, AutoTokenizer, AutoModelForSeq2SeqLM

class TranslationTool:
    """多源翻译工具"""
    
    def __init__(self, use_local_model: bool = True):
        """
        初始化翻译工具
        
        Args:
            use_local_model: 是否使用本地翻译模型
        """
        self.use_local_model = use_local_model
        self.google_translator = None
        self.local_translator = None
        self.translation_cache = {}
        self.cache_file = "translation_cache.json"
        
        # 加载缓存
        self._load_cache()
        
        # 初始化翻译器
        self._init_translators()
    
    def _load_cache(self):
        """加载翻译缓存"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.translation_cache = json.load(f)
                print(f"已加载 {len(self.translation_cache)} 条翻译缓存")
            except Exception as e:
                print(f"加载缓存失败: {e}")
                self.translation_cache = {}
    
    def _save_cache(self):
        """保存翻译缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.translation_cache, f, ensure_ascii=False, indent=2)
            print(f"已保存 {len(self.translation_cache)} 条翻译缓存")
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def _init_translators(self):
        """初始化翻译器"""
        print("正在初始化翻译器...")
        
        # 初始化Google翻译
        try:
            self.google_translator = Translator()
            print("✅ Google翻译器初始化成功")
        except Exception as e:
            print(f"❌ Google翻译器初始化失败: {e}")
        
        # 初始化本地翻译模型
        if self.use_local_model:
            try:
                print("正在加载本地翻译模型...")
                # 使用Helsinki-NLP的英中翻译模型
                model_name = "Helsinki-NLP/opus-mt-en-zh"
                self.local_translator = pipeline(
                    "translation", 
                    model=model_name,
                    device=-1  # 使用CPU
                )
                print("✅ 本地翻译模型加载成功")
            except Exception as e:
                print(f"❌ 本地翻译模型加载失败: {e}")
                self.local_translator = None
    
    def translate_text(self, text: str, method: str = "auto") -> str:
        """
        翻译文本
        
        Args:
            text: 要翻译的英文文本
            method: 翻译方法 ("google", "local", "auto")
        
        Returns:
            翻译后的中文文本
        """
        if not text or not text.strip():
            return ""
        
        text = text.strip()
        
        # 检查缓存
        if text in self.translation_cache:
            return self.translation_cache[text]
        
        translated = ""
        
        # 选择翻译方法
        if method == "auto":
            # 优先使用本地模型，失败则使用Google翻译
            if self.local_translator:
                try:
                    translated = self._translate_with_local(text)
                except Exception as e:
                    print(f"本地翻译失败，尝试Google翻译: {e}")
                    translated = self._translate_with_google(text)
            else:
                translated = self._translate_with_google(text)
        elif method == "local":
            translated = self._translate_with_local(text)
        elif method == "google":
            translated = self._translate_with_google(text)
        
        # 缓存翻译结果
        if translated:
            self.translation_cache[text] = translated
            
            # 每100条翻译保存一次缓存
            if len(self.translation_cache) % 100 == 0:
                self._save_cache()
        
        return translated
    
    def _translate_with_local(self, text: str) -> str:
        """使用本地模型翻译"""
        if not self.local_translator:
            raise Exception("本地翻译模型未初始化")
        
        try:
            # 限制文本长度，避免模型处理过长文本
            if len(text) > 500:
                text = text[:500]
            
            result = self.local_translator(text, max_length=512)
            translated = result[0]['translation_text']
            
            # 清理翻译结果
            translated = translated.strip()
            return translated
            
        except Exception as e:
            raise Exception(f"本地翻译失败: {e}")
    
    def _translate_with_google(self, text: str) -> str:
        """使用Google翻译"""
        if not self.google_translator:
            raise Exception("Google翻译器未初始化")
        
        try:
            # 添加延迟避免API限制
            time.sleep(0.1)
            
            result = self.google_translator.translate(text, src='en', dest='zh')
            translated = result.text.strip()
            return translated
            
        except Exception as e:
            raise Exception(f"Google翻译失败: {e}")
    
    def translate_dataframe(self, df: pd.DataFrame, 
                          text_column: str = 'description',
                          output_column: str = 'description_zh',
                          method: str = "auto",
                          batch_size: int = 100) -> pd.DataFrame:
        """
        翻译DataFrame中的文本列
        
        Args:
            df: 输入DataFrame
            text_column: 要翻译的列名
            output_column: 输出列名
            method: 翻译方法
            batch_size: 批处理大小
        
        Returns:
            包含翻译结果的DataFrame
        """
        print(f"开始翻译 {len(df)} 条记录...")
        
        df_copy = df.copy()
        translations = []
        
        for i, text in enumerate(df_copy[text_column]):
            try:
                translated = self.translate_text(str(text), method=method)
                translations.append(translated)
                
                if (i + 1) % batch_size == 0:
                    print(f"已翻译 {i + 1}/{len(df)} 条记录")
                    
            except Exception as e:
                print(f"翻译第 {i+1} 条记录失败: {e}")
                translations.append(str(text))  # 翻译失败时保留原文
        
        df_copy[output_column] = translations
        
        # 保存最终缓存
        self._save_cache()
        
        print(f"翻译完成！共翻译 {len(translations)} 条记录")
        return df_copy


class ChineseTableGenerator:
    """中文表生成器"""
    
    def __init__(self, data_dir: str = "split_files", output_dir: str = "chinese_tables"):
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.translator = TranslationTool(use_local_model=True)
    
    def generate_chinese_tables(self, translation_method: str = "auto"):
        """生成所有表的中文版本"""
        print("开始生成中文翻译表...")
        
        csv_files = list(self.data_dir.glob("*.csv"))
        if not csv_files:
            raise FileNotFoundError(f"在目录 {self.data_dir} 中未找到CSV文件")
        
        total_files = len(csv_files)
        
        for i, file_path in enumerate(csv_files, 1):
            print(f"\n处理文件 {i}/{total_files}: {file_path.name}")
            
            try:
                # 读取原始数据
                df = pd.read_csv(file_path)
                
                if 'description' not in df.columns:
                    print(f"跳过文件 {file_path.name}: 缺少description列")
                    continue
                
                # 翻译描述字段
                df_translated = self.translator.translate_dataframe(
                    df, 
                    text_column='description',
                    output_column='description_zh',
                    method=translation_method
                )
                
                # 保存中文版本
                output_path = self.output_dir / f"zh_{file_path.name}"
                df_translated.to_csv(output_path, index=False, encoding='utf-8-sig')
                
                print(f"✅ 已保存中文版本: {output_path}")
                
            except Exception as e:
                print(f"❌ 处理文件 {file_path.name} 失败: {e}")
        
        print(f"\n🎉 中文翻译表生成完成！")
        print(f"输出目录: {self.output_dir}")
    
    def create_translation_mapping(self) -> Dict[str, str]:
        """创建英中对照映射表"""
        print("创建英中对照映射表...")
        
        mapping = {}
        csv_files = list(self.data_dir.glob("*.csv"))
        
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                if 'description' in df.columns:
                    for _, row in df.iterrows():
                        english = str(row['description']).strip()
                        if english and english not in mapping:
                            chinese = self.translator.translate_text(english)
                            if chinese:
                                mapping[english] = chinese
                                
            except Exception as e:
                print(f"处理文件 {file_path.name} 时出错: {e}")
        
        # 保存映射表
        mapping_file = self.output_dir / "en_zh_mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 英中对照映射表已保存: {mapping_file}")
        print(f"共包含 {len(mapping)} 条映射")
        
        return mapping


def main():
    """主函数 - 演示翻译功能"""
    print("🌐 开源翻译工具演示")
    print("=" * 50)
    
    try:
        # 创建中文表生成器
        generator = ChineseTableGenerator()
        
        # 测试翻译功能
        print("\n1. 测试翻译功能:")
        test_texts = [
            "Market Capitalization",
            "Price to Earnings Ratio",
            "Cash Flow from Operations",
            "Return on Equity",
            "Dividend Yield"
        ]
        
        for text in test_texts:
            translated = generator.translator.translate_text(text)
            print(f"   {text} → {translated}")
        
        # 询问是否生成完整的中文表
        print(f"\n2. 是否生成完整的中文翻译表？")
        print("   这将翻译所有59,421条记录，可能需要较长时间...")
        
        # 为了演示，我们只处理一个小文件
        print("\n3. 演示：处理一个示例文件")
        
        # 选择一个较小的文件进行演示
        sample_file = Path("split_files/CHN_0_TOP2000U.csv")
        if sample_file.exists():
            print(f"处理示例文件: {sample_file}")
            
            df = pd.read_csv(sample_file)
            print(f"原始记录数: {len(df)}")
            
            # 只翻译前10条记录作为演示
            df_sample = df.head(10)
            df_translated = generator.translator.translate_dataframe(
                df_sample,
                method="auto"
            )
            
            # 显示翻译结果
            print("\n翻译结果示例:")
            for i, row in df_translated.iterrows():
                print(f"{i+1}. {row['id']}")
                print(f"   英文: {row['description']}")
                print(f"   中文: {row['description_zh']}")
                print()
        
        print("🎉 翻译工具演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
