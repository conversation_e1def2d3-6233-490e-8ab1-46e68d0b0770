#!/usr/bin/env python3
"""
最终版双语搜索系统 - 无需登录
集成所有高级搜索功能，无需用户认证，直接使用
"""

from flask import Flask, render_template, request, jsonify, send_file
import pandas as pd
import json
import time
import os
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from advanced_search_engine import AdvancedSearchEngine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 全局变量
search_engine = None

def init_search_engine():
    """初始化搜索引擎"""
    global search_engine
    
    if search_engine is None:
        logger.info("正在初始化最终版搜索引擎...")
        try:
            search_engine = AdvancedSearchEngine()
            logger.info("✅ 最终版搜索引擎初始化完成")
            return True
        except Exception as e:
            logger.error(f"❌ 搜索引擎初始化失败: {e}")
            return False
    
    return True

@app.route('/')
def index():
    """主页"""
    return render_template('final_index.html')

@app.route('/api/init', methods=['POST'])
def api_init():
    """初始化API"""
    try:
        success = init_search_engine()
        
        if success and search_engine:
            data = search_engine.data
            
            stats = {
                "total_records": len(data),
                "bilingual_records": len(data[data['description_zh'].notna()]) if 'description_zh' in data.columns else 0,
                "unique_tables": data['table_name'].nunique() if 'table_name' in data.columns else 0,
                "regions": list(data['region'].unique()) if 'region' in data.columns else [],
                "types": list(data['type'].unique()) if 'type' in data.columns else [],
                "categories": list(data['category.name'].unique()) if 'category.name' in data.columns else []
            }
            
            return jsonify({
                "success": True,
                "message": "最终版搜索引擎初始化成功",
                "stats": stats
            })
        else:
            return jsonify({
                "success": False,
                "error": "搜索引擎初始化失败"
            })
            
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/search', methods=['POST'])
def api_search():
    """最终版搜索API"""
    try:
        if not search_engine:
            return jsonify({
                "success": False,
                "error": "搜索引擎未初始化"
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                "success": False,
                "error": "查询不能为空"
            })
        
        # 搜索参数
        top_k = min(int(data.get('top_k', 20)), 100)
        language = data.get('language', 'auto')
        enable_synonyms = data.get('enable_synonyms', True)
        enable_spell_check = data.get('enable_spell_check', True)
        enable_semantic = data.get('enable_semantic', True)
        enable_fuzzy = data.get('enable_fuzzy', True)
        fuzzy_threshold = float(data.get('fuzzy_threshold', 0.6))
        
        # 执行搜索
        start_time = time.time()
        
        results = search_engine.advanced_search(
            query=query,
            top_k=top_k,
            language=language,
            enable_synonyms=enable_synonyms,
            enable_spell_check=enable_spell_check,
            enable_semantic=enable_semantic,
            enable_fuzzy=enable_fuzzy,
            fuzzy_threshold=fuzzy_threshold
        )
        
        search_time = time.time() - start_time
        
        # 转换结果为JSON格式
        json_results = []
        for result in results:
            json_results.append({
                "id": result.id,
                "description": result.description,
                "description_zh": result.description_zh,
                "similarity_score": result.similarity_score,
                "match_type": result.match_type,
                "match_details": result.match_details,
                "region": result.region,
                "universe": result.universe,
                "type": result.type,
                "category": result.category,
                "subcategory": result.subcategory,
                "dataset_name": result.dataset_name,
                "coverage": result.coverage,
                "user_count": result.user_count,
                "alpha_count": result.alpha_count,
                "table_name": result.table_name
            })
        
        # 检测语言
        detected_language = "zh" if any('\u4e00' <= char <= '\u9fff' for char in query) else "en"
        
        response_data = {
            "success": True,
            "results": json_results,
            "search_time": search_time,
            "total_results": len(json_results),
            "query": query,
            "detected_language": detected_language,
            "search_features": {
                "synonyms": enable_synonyms,
                "spell_check": enable_spell_check,
                "semantic": enable_semantic,
                "fuzzy": enable_fuzzy
            }
        }
        
        logger.info(f"最终版搜索: '{query}' -> {len(json_results)} 结果, {search_time:.3f}秒")
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/export', methods=['POST'])
def api_export():
    """导出搜索结果"""
    try:
        data = request.get_json()
        results = data.get('results', [])
        
        if not results:
            return jsonify({
                "success": False,
                "error": "没有结果可导出"
            })
        
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 生成文件名
        timestamp = int(time.time())
        filename = f"final_search_results_{timestamp}.csv"
        filepath = f"downloads/{filename}"
        
        # 确保下载目录存在
        os.makedirs("downloads", exist_ok=True)
        
        # 保存CSV文件
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        logger.info(f"导出文件: {filename} ({len(results)} 条记录)")
        
        return jsonify({
            "success": True,
            "download_url": f"/download/{filename}",
            "filename": filename
        })
        
    except Exception as e:
        logger.error(f"导出失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        filepath = f"downloads/{filename}"
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            return "文件不存在", 404
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return str(e), 500

@app.route('/api/stats')
def api_stats():
    """获取统计信息"""
    try:
        if not search_engine:
            return jsonify({
                "success": False,
                "error": "搜索引擎未初始化"
            })
        
        data = search_engine.data
        
        stats = {
            "total_records": len(data),
            "bilingual_records": len(data[data['description_zh'].notna()]) if 'description_zh' in data.columns else 0,
            "unique_tables": data['table_name'].nunique() if 'table_name' in data.columns else 0,
            "regions": list(data['region'].unique()) if 'region' in data.columns else [],
            "types": list(data['type'].unique()) if 'type' in data.columns else [],
            "categories": list(data['category.name'].unique()) if 'category.name' in data.columns else []
        }
        
        return jsonify({
            "success": True,
            "stats": stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

if __name__ == '__main__':
    print("🚀 启动最终版双语数据字段检索系统...")
    print("=" * 60)
    print("🌟 功能特色:")
    print("  🧠 高级语义搜索 - 理解查询意图")
    print("  🔍 智能模糊匹配 - 容错搜索")
    print("  📚 同义词扩展 - 扩大搜索范围")
    print("  🔤 拼写纠错 - 自动修正错误")
    print("  🌐 中英文双语 - 无缝切换")
    print("  ⚡ 毫秒级响应 - 极速搜索")
    print("  💾 结果导出 - 数据保存")
    print("  🎯 无需登录 - 直接使用")
    print("=" * 60)
    print("访问 http://localhost:5004 开始使用")
    
    # 预初始化搜索引擎
    init_search_engine()
    
    app.run(
        host='0.0.0.0',
        port=5004,
        debug=False,
        threaded=True
    )
