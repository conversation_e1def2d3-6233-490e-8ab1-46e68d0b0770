#!/usr/bin/env python3
"""
数据字段检索器演示脚本
展示不同搜索方法的效果对比
"""

from field_searcher import FieldSearcher
import time

def compare_search_methods(searcher, query, top_k=5):
    """比较不同搜索方法的效果"""
    methods = ['cosine', 'fuzzy', 'keyword', 'hybrid']
    
    print(f"\n{'='*80}")
    print(f"搜索查询: '{query}' - 不同方法对比")
    print(f"{'='*80}")
    
    for method in methods:
        print(f"\n--- {method.upper()} 方法 ---")
        start_time = time.time()
        
        results = searcher.search(
            query=query,
            top_k=top_k,
            similarity_method=method,
            min_score=0.05
        )
        
        end_time = time.time()
        
        print(f"搜索时间: {end_time - start_time:.3f}秒")
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")
        else:
            print("未找到匹配结果")

def demo_advanced_search(searcher):
    """演示高级搜索功能"""
    print(f"\n{'='*80}")
    print("高级搜索功能演示")
    print(f"{'='*80}")
    
    # 按地区过滤搜索
    print("\n1. 按地区过滤 (CHN):")
    results = searcher.search(
        query="market cap",
        top_k=5,
        region_filter="CHN"
    )
    for i, result in enumerate(results, 1):
        print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description} ({result.region})")
    
    # 按类型过滤搜索
    print("\n2. 按类型过滤 (MATRIX):")
    results = searcher.search(
        query="price",
        top_k=5,
        type_filter="MATRIX"
    )
    for i, result in enumerate(results, 1):
        print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description} ({result.type})")
    
    # 设置最小相似度阈值
    print("\n3. 高相似度阈值 (>0.3):")
    results = searcher.search(
        query="volume",
        top_k=10,
        min_score=0.3
    )
    for i, result in enumerate(results, 1):
        print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")

def main():
    """主演示函数"""
    print("数据字段检索器演示")
    print("正在初始化...")
    
    try:
        # 初始化搜索器
        searcher = FieldSearcher()
        
        # 显示数据统计
        stats = searcher.get_statistics()
        print(f"\n数据概览:")
        print(f"- 总记录数: {stats['total_records']:,}")
        print(f"- 地区数: {stats['unique_regions']}")
        print(f"- 类型数: {stats['unique_types']}")
        print(f"- 分类数: {stats['unique_categories']}")
        
        # 测试查询列表
        test_queries = [
            "market capitalization",  # 英文查询
            "价格",                   # 中文查询
            "volume trading",         # 英文组合
            "现金流",                 # 中文财务术语
            "beta risk",             # 金融术语
            "revenue income"         # 收入相关
        ]
        
        # 对每个查询进行方法对比
        for query in test_queries:
            compare_search_methods(searcher, query, top_k=3)
        
        # 演示高级搜索功能
        demo_advanced_search(searcher)
        
        # 性能测试
        print(f"\n{'='*80}")
        print("性能测试")
        print(f"{'='*80}")
        
        start_time = time.time()
        for _ in range(10):
            searcher.search("market cap", top_k=10)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        print(f"平均搜索时间 (10次): {avg_time:.3f}秒")
        
        print(f"\n演示完成！")
        print("要使用交互式搜索，请运行: python interactive_search.py")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
