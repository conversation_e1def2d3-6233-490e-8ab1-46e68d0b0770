#!/usr/bin/env python3
"""
Web API测试脚本
测试Web应用的各个API端点
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_init_api():
    """测试初始化API"""
    print("测试初始化API...")
    
    response = requests.post(f"{BASE_URL}/api/init")
    data = response.json()
    
    if data['success']:
        print("✓ 初始化成功")
        print(f"  总记录数: {data['stats']['total_records']:,}")
        print(f"  数据表数: {data['stats']['unique_tables']}")
        print(f"  地区数: {len(data['regions'])}")
        print(f"  分类数: {len(data['categories'])}")
        return True
    else:
        print(f"✗ 初始化失败: {data['error']}")
        return False

def test_search_api():
    """测试搜索API"""
    print("\n测试搜索API...")
    
    # 测试基础搜索
    search_data = {
        "query": "market cap",
        "top_k": 5,
        "similarity_method": "hybrid",
        "min_score": 0.1
    }
    
    response = requests.post(
        f"{BASE_URL}/api/search",
        json=search_data,
        headers={'Content-Type': 'application/json'}
    )
    
    data = response.json()
    
    if data['success']:
        print(f"✓ 搜索成功")
        print(f"  查询: {data['query']}")
        print(f"  结果数: {data['total_results']}")
        print(f"  搜索时间: {data['search_time']:.3f}秒")
        
        # 显示前3个结果
        for i, result in enumerate(data['results'][:3], 1):
            print(f"  {i}. [{result['similarity_score']:.4f}] {result['id']}: {result['description'][:50]}...")
        
        return data['results']
    else:
        print(f"✗ 搜索失败: {data['error']}")
        return []

def test_filtered_search():
    """测试过滤搜索"""
    print("\n测试过滤搜索...")
    
    # 测试地区过滤
    search_data = {
        "query": "price",
        "top_k": 3,
        "similarity_method": "hybrid",
        "region_filter": "CHN"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/search",
        json=search_data,
        headers={'Content-Type': 'application/json'}
    )
    
    data = response.json()
    
    if data['success']:
        print(f"✓ 地区过滤搜索成功")
        print(f"  过滤条件: 地区=CHN")
        print(f"  结果数: {data['total_results']}")
        
        for i, result in enumerate(data['results'], 1):
            print(f"  {i}. {result['id']} (地区: {result['region']})")
    else:
        print(f"✗ 过滤搜索失败: {data['error']}")

def test_chinese_search():
    """测试中文搜索"""
    print("\n测试中文搜索...")
    
    chinese_queries = ["价格", "现金流", "市值"]
    
    for query in chinese_queries:
        search_data = {
            "query": query,
            "top_k": 3,
            "similarity_method": "hybrid"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/search",
            json=search_data,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        
        if data['success']:
            print(f"✓ 中文搜索 '{query}' 成功，找到 {data['total_results']} 个结果")
            if data['results']:
                best_result = data['results'][0]
                print(f"  最佳匹配: [{best_result['similarity_score']:.4f}] {best_result['id']}")
        else:
            print(f"✗ 中文搜索 '{query}' 失败: {data['error']}")

def test_table_info_api():
    """测试表格信息API"""
    print("\n测试表格信息API...")
    
    # 先获取表格列表
    response = requests.post(f"{BASE_URL}/api/init")
    data = response.json()
    
    if data['success'] and data['tables']:
        table_name = list(data['tables'].keys())[0]
        
        response = requests.get(f"{BASE_URL}/api/table/{table_name}")
        table_data = response.json()
        
        if table_data['success']:
            print(f"✓ 表格信息获取成功")
            print(f"  表格: {table_name}")
            print(f"  记录数: {table_data['table_info']['record_count']}")
            print(f"  地区: {', '.join(table_data['table_info']['regions'])}")
        else:
            print(f"✗ 表格信息获取失败: {table_data['error']}")

def test_export_api():
    """测试导出API"""
    print("\n测试导出API...")
    
    # 先执行一个搜索获取结果
    search_data = {
        "query": "volume",
        "top_k": 5,
        "similarity_method": "cosine"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/search",
        json=search_data,
        headers={'Content-Type': 'application/json'}
    )
    
    search_result = response.json()
    
    if search_result['success'] and search_result['results']:
        # 测试导出
        export_data = {
            "results": search_result['results']
        }
        
        response = requests.post(
            f"{BASE_URL}/api/export",
            json=export_data,
            headers={'Content-Type': 'application/json'}
        )
        
        data = response.json()
        
        if data['success']:
            print(f"✓ 导出成功")
            print(f"  文件名: {data['filename']}")
            print(f"  下载链接: {data['download_url']}")
        else:
            print(f"✗ 导出失败: {data['error']}")
    else:
        print("✗ 无法获取搜索结果进行导出测试")

def test_stats_api():
    """测试统计信息API"""
    print("\n测试统计信息API...")
    
    response = requests.get(f"{BASE_URL}/api/stats")
    data = response.json()
    
    if data['success']:
        print("✓ 统计信息获取成功")
        stats = data['stats']
        print(f"  总记录数: {stats['total_records']:,}")
        print(f"  数据表数: {stats['unique_tables']}")
        print(f"  地区数: {stats['unique_regions']}")
        print(f"  类型数: {stats['unique_types']}")
        print(f"  分类数: {stats['unique_categories']}")
    else:
        print(f"✗ 统计信息获取失败: {data['error']}")

def main():
    """主测试函数"""
    print("数据字段检索器 Web API 测试")
    print("="*50)
    
    try:
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(2)
        
        # 测试各个API
        if test_init_api():
            test_search_api()
            test_filtered_search()
            test_chinese_search()
            test_table_info_api()
            test_export_api()
            test_stats_api()
        
        print("\n" + "="*50)
        print("测试完成！")
        print("Web应用运行正常，可以在浏览器中访问 http://localhost:5000")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器")
        print("请确保Web应用正在运行: python web_app.py")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
