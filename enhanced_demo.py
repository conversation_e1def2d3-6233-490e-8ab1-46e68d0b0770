#!/usr/bin/env python3
"""
增强版演示脚本
展示新功能：ID匹配、表格分离、分类过滤等
"""

from field_searcher import FieldSearcher
import time

def test_id_matching(searcher):
    """测试ID匹配功能"""
    print(f"\n{'='*80}")
    print("ID匹配功能测试")
    print(f"{'='*80}")
    
    # 测试完全匹配
    print("\n1. 完全ID匹配测试:")
    results = searcher.search("cap", top_k=5, similarity_method='hybrid')
    for i, result in enumerate(results[:3], 1):
        print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")
    
    # 测试部分匹配
    print("\n2. 部分ID匹配测试:")
    results = searcher.search("market", top_k=5, similarity_method='hybrid')
    for i, result in enumerate(results[:3], 1):
        print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")

def test_table_separation(searcher):
    """测试表格分离功能"""
    print(f"\n{'='*80}")
    print("表格分离功能测试")
    print(f"{'='*80}")
    
    # 获取所有表格信息
    tables = searcher.get_tables()
    print(f"共有 {len(tables)} 个数据表:")
    
    # 显示前5个表格的信息
    for i, (table_name, metadata) in enumerate(list(tables.items())[:5], 1):
        print(f"{i}. {table_name}: {metadata['record_count']} 条记录")
        if metadata['regions']:
            print(f"   地区: {', '.join(metadata['regions'])}")
        if metadata['categories']:
            print(f"   分类: {', '.join(metadata['categories'][:3])}...")
    
    # 在特定表中搜索
    print(f"\n在特定表中搜索示例:")
    table_name = list(tables.keys())[0]  # 选择第一个表
    print(f"在表 '{table_name}' 中搜索 'price':")
    
    results = searcher.search_by_table("price", table_name, top_k=3)
    for i, result in enumerate(results, 1):
        print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description} (表: {result.table_name})")

def test_category_filtering(searcher):
    """测试分类过滤功能"""
    print(f"\n{'='*80}")
    print("分类过滤功能测试")
    print(f"{'='*80}")
    
    # 获取所有分类
    categories = searcher.get_categories()
    print(f"共有 {len(categories)} 个分类:")
    for i, category in enumerate(categories[:10], 1):
        print(f"{i}. {category}")
    
    if len(categories) > 10:
        print(f"... 还有 {len(categories) - 10} 个分类")
    
    # 按分类搜索
    print(f"\n按分类搜索示例:")
    if categories:
        category = categories[0]  # 选择第一个分类
        print(f"在分类 '{category}' 中搜索 'market':")
        
        results = searcher.search("market", top_k=3, category_filter=category)
        for i, result in enumerate(results, 1):
            print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")
            print(f"   分类: {result.category}")

def test_combined_filters(searcher):
    """测试组合过滤功能"""
    print(f"\n{'='*80}")
    print("组合过滤功能测试")
    print(f"{'='*80}")
    
    # 获取可用的过滤选项
    regions = searcher.get_regions()
    types = searcher.get_types()
    tables = list(searcher.get_tables().keys())
    
    print("可用过滤选项:")
    print(f"地区: {', '.join(regions[:5])}")
    print(f"类型: {', '.join(types[:5])}")
    print(f"表格: {', '.join(tables[:3])}...")
    
    # 组合过滤搜索
    if regions and types:
        print(f"\n组合过滤搜索示例:")
        print(f"搜索条件: 查询='volume', 地区='{regions[0]}', 类型='{types[0]}'")
        
        results = searcher.search(
            "volume", 
            top_k=3, 
            region_filter=regions[0],
            type_filter=types[0]
        )
        
        for i, result in enumerate(results, 1):
            print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")
            print(f"   地区: {result.region}, 类型: {result.type}")

def test_similarity_tie_breaking(searcher):
    """测试相似度平局时的ID匹配"""
    print(f"\n{'='*80}")
    print("相似度平局时ID匹配测试")
    print(f"{'='*80}")
    
    # 搜索可能产生相同相似度的查询
    test_queries = ["volume", "price", "market"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        results = searcher.search(query, top_k=5, similarity_method='hybrid')
        
        # 检查是否有相同的相似度分数
        scores = [r.similarity_score for r in results]
        unique_scores = len(set(scores))
        
        print(f"前5个结果中有 {5 - unique_scores} 个平局情况")
        
        for i, result in enumerate(results[:3], 1):
            print(f"{i}. [{result.similarity_score:.4f}] {result.id}: {result.description}")

def performance_comparison(searcher):
    """性能对比测试"""
    print(f"\n{'='*80}")
    print("性能对比测试")
    print(f"{'='*80}")
    
    test_queries = ["market cap", "cash flow", "price", "volume", "beta"]
    
    print("测试不同搜索方法的性能:")
    
    for method in ['cosine', 'hybrid']:
        print(f"\n{method.upper()} 方法:")
        total_time = 0
        
        for query in test_queries:
            start_time = time.time()
            results = searcher.search(query, top_k=10, similarity_method=method)
            search_time = time.time() - start_time
            total_time += search_time
            
            print(f"  '{query}': {search_time:.3f}秒, {len(results)}个结果")
        
        avg_time = total_time / len(test_queries)
        print(f"  平均时间: {avg_time:.3f}秒")

def main():
    """主演示函数"""
    print("增强版数据字段检索器演示")
    print("展示新功能：ID匹配、表格分离、分类过滤等")
    
    try:
        # 初始化搜索器
        print("\n正在初始化搜索器...")
        searcher = FieldSearcher()
        
        # 显示基本统计信息
        stats = searcher.get_statistics()
        print(f"\n数据概览:")
        print(f"- 总记录数: {stats['total_records']:,}")
        print(f"- 数据表数: {stats['unique_tables']}")
        print(f"- 地区数: {stats['unique_regions']}")
        print(f"- 类型数: {stats['unique_types']}")
        print(f"- 分类数: {stats['unique_categories']}")
        
        # 运行各项测试
        test_id_matching(searcher)
        test_table_separation(searcher)
        test_category_filtering(searcher)
        test_combined_filters(searcher)
        test_similarity_tie_breaking(searcher)
        performance_comparison(searcher)
        
        print(f"\n{'='*80}")
        print("演示完成！")
        print("要使用Web界面，请运行: python web_app.py")
        print("然后访问 http://localhost:5000")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
