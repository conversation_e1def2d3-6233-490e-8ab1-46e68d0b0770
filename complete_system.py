#!/usr/bin/env python3
"""
完整版双语搜索系统启动器
集成所有功能的完整版系统
"""

from flask import Flask, render_template, request, jsonify, send_file, session
import pandas as pd
import json
import time
import os
import hashlib
import logging
from pathlib import Path
from datetime import datetime
import sqlite3
from functools import wraps
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from advanced_search_engine import AdvancedSearchEngine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'complete-system-secret-key'

# 全局变量
search_engine = None
user_db = "complete_users.db"

class SimpleUserManager:
    """简化的用户管理系统"""
    
    def __init__(self, db_path: str = "complete_users.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化用户数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                search_count INTEGER DEFAULT 0
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS search_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                query TEXT NOT NULL,
                results_count INTEGER,
                search_time REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_user(self, username: str, password: str, email: str = None) -> bool:
        """创建用户"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            cursor.execute(
                "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
                (username, password_hash, email)
            )
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
    
    def authenticate_user(self, username: str, password: str) -> dict:
        """用户认证"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute(
            "SELECT id, username, email, search_count FROM users WHERE username = ? AND password_hash = ?",
            (username, password_hash)
        )
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'username': user[1],
                'email': user[2],
                'search_count': user[3]
            }
        return None
    
    def log_search(self, user_id: int, query: str, results_count: int, search_time: float):
        """记录搜索历史"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "INSERT INTO search_history (user_id, query, results_count, search_time) VALUES (?, ?, ?, ?)",
            (user_id, query, results_count, search_time)
        )
        
        cursor.execute(
            "UPDATE users SET search_count = search_count + 1 WHERE id = ?",
            (user_id,)
        )
        
        conn.commit()
        conn.close()

# 初始化用户管理器
user_manager = SimpleUserManager()

def require_auth(f):
    """认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': '需要登录'}), 401
        return f(*args, **kwargs)
    return decorated_function

def init_search_engine():
    """初始化搜索引擎"""
    global search_engine
    
    if search_engine is None:
        logger.info("正在初始化完整版搜索引擎...")
        try:
            search_engine = AdvancedSearchEngine()
            logger.info("✅ 完整版搜索引擎初始化完成")
            return True
        except Exception as e:
            logger.error(f"❌ 搜索引擎初始化失败: {e}")
            return False
    
    return True

@app.route('/')
def index():
    """主页"""
    return render_template('complete_index.html')

@app.route('/login')
def login_page():
    """登录页面"""
    return render_template('login.html')

@app.route('/register')
def register_page():
    """注册页面"""
    return render_template('register.html')

@app.route('/api/register', methods=['POST'])
def api_register():
    """用户注册API"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        email = data.get('email', '').strip()
        
        if not username or not password:
            return jsonify({'success': False, 'error': '用户名和密码不能为空'})
        
        if len(password) < 6:
            return jsonify({'success': False, 'error': '密码长度至少6位'})
        
        if user_manager.create_user(username, password, email):
            logger.info(f"新用户注册: {username}")
            return jsonify({'success': True, 'message': '注册成功'})
        else:
            return jsonify({'success': False, 'error': '用户名已存在'})
            
    except Exception as e:
        logger.error(f"注册失败: {e}")
        return jsonify({'success': False, 'error': '注册失败'})

@app.route('/api/login', methods=['POST'])
def api_login():
    """用户登录API"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            return jsonify({'success': False, 'error': '用户名和密码不能为空'})
        
        user = user_manager.authenticate_user(username, password)
        
        if user:
            session['user_id'] = user['id']
            session['username'] = user['username']
            logger.info(f"用户登录: {username}")
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': user
            })
        else:
            return jsonify({'success': False, 'error': '用户名或密码错误'})
            
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'success': False, 'error': '登录失败'})

@app.route('/api/logout', methods=['POST'])
def api_logout():
    """用户登出API"""
    username = session.get('username', 'Unknown')
    session.clear()
    logger.info(f"用户登出: {username}")
    return jsonify({'success': True, 'message': '登出成功'})

@app.route('/api/init', methods=['POST'])
@require_auth
def api_init():
    """初始化API"""
    try:
        success = init_search_engine()
        
        if success and search_engine:
            data = search_engine.data
            
            stats = {
                "total_records": len(data),
                "bilingual_records": len(data[data['description_zh'].notna()]) if 'description_zh' in data.columns else 0,
                "unique_tables": data['table_name'].nunique() if 'table_name' in data.columns else 0,
                "regions": list(data['region'].unique()) if 'region' in data.columns else [],
                "types": list(data['type'].unique()) if 'type' in data.columns else [],
                "categories": list(data['category.name'].unique()) if 'category.name' in data.columns else []
            }
            
            return jsonify({
                "success": True,
                "message": "完整版搜索引擎初始化成功",
                "stats": stats
            })
        else:
            return jsonify({
                "success": False,
                "error": "搜索引擎初始化失败"
            })
            
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/search', methods=['POST'])
@require_auth
def api_search():
    """完整版搜索API"""
    try:
        if not search_engine:
            return jsonify({
                "success": False,
                "error": "搜索引擎未初始化"
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                "success": False,
                "error": "查询不能为空"
            })
        
        # 搜索参数
        top_k = min(int(data.get('top_k', 20)), 100)
        language = data.get('language', 'auto')
        enable_synonyms = data.get('enable_synonyms', True)
        enable_spell_check = data.get('enable_spell_check', True)
        enable_semantic = data.get('enable_semantic', True)
        enable_fuzzy = data.get('enable_fuzzy', True)
        fuzzy_threshold = float(data.get('fuzzy_threshold', 0.6))
        
        # 执行搜索
        start_time = time.time()
        
        results = search_engine.advanced_search(
            query=query,
            top_k=top_k,
            language=language,
            enable_synonyms=enable_synonyms,
            enable_spell_check=enable_spell_check,
            enable_semantic=enable_semantic,
            enable_fuzzy=enable_fuzzy,
            fuzzy_threshold=fuzzy_threshold
        )
        
        search_time = time.time() - start_time
        
        # 转换结果为JSON格式
        json_results = []
        for result in results:
            json_results.append({
                "id": result.id,
                "description": result.description,
                "description_zh": result.description_zh,
                "similarity_score": result.similarity_score,
                "match_type": result.match_type,
                "match_details": result.match_details,
                "region": result.region,
                "universe": result.universe,
                "type": result.type,
                "category": result.category,
                "subcategory": result.subcategory,
                "dataset_name": result.dataset_name,
                "coverage": result.coverage,
                "user_count": result.user_count,
                "alpha_count": result.alpha_count,
                "table_name": result.table_name
            })
        
        # 检测语言
        detected_language = "zh" if any('\u4e00' <= char <= '\u9fff' for char in query) else "en"
        
        response_data = {
            "success": True,
            "results": json_results,
            "search_time": search_time,
            "total_results": len(json_results),
            "query": query,
            "detected_language": detected_language,
            "search_features": {
                "synonyms": enable_synonyms,
                "spell_check": enable_spell_check,
                "semantic": enable_semantic,
                "fuzzy": enable_fuzzy
            }
        }
        
        # 记录搜索历史
        user_id = session.get('user_id')
        if user_id:
            user_manager.log_search(user_id, query, len(json_results), search_time)
        
        logger.info(f"完整版搜索: '{query}' -> {len(json_results)} 结果, {search_time:.3f}秒")
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/api/export', methods=['POST'])
@require_auth
def api_export():
    """导出搜索结果"""
    try:
        data = request.get_json()
        results = data.get('results', [])
        
        if not results:
            return jsonify({
                "success": False,
                "error": "没有结果可导出"
            })
        
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 生成文件名
        timestamp = int(time.time())
        username = session.get('username', 'user')
        filename = f"complete_search_results_{username}_{timestamp}.csv"
        filepath = f"downloads/{filename}"
        
        # 确保下载目录存在
        os.makedirs("downloads", exist_ok=True)
        
        # 保存CSV文件
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        logger.info(f"导出文件: {filename} ({len(results)} 条记录)")
        
        return jsonify({
            "success": True,
            "download_url": f"/download/{filename}",
            "filename": filename
        })
        
    except Exception as e:
        logger.error(f"导出失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/download/<filename>')
@require_auth
def download_file(filename):
    """下载文件"""
    try:
        filepath = f"downloads/{filename}"
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            return "文件不存在", 404
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return str(e), 500

@app.route('/api/stats')
@require_auth
def api_stats():
    """获取统计信息"""
    try:
        if not search_engine:
            return jsonify({
                "success": False,
                "error": "搜索引擎未初始化"
            })
        
        data = search_engine.data
        
        stats = {
            "total_records": len(data),
            "bilingual_records": len(data[data['description_zh'].notna()]) if 'description_zh' in data.columns else 0,
            "unique_tables": data['table_name'].nunique() if 'table_name' in data.columns else 0,
            "regions": list(data['region'].unique()) if 'region' in data.columns else [],
            "types": list(data['type'].unique()) if 'type' in data.columns else [],
            "categories": list(data['category.name'].unique()) if 'category.name' in data.columns else []
        }
        
        return jsonify({
            "success": True,
            "stats": stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        })

if __name__ == '__main__':
    print("🚀 启动完整版双语数据字段检索系统...")
    print("=" * 60)
    print("🌟 功能特色:")
    print("  🧠 高级语义搜索")
    print("  🔍 智能模糊匹配") 
    print("  📚 同义词扩展")
    print("  🔤 拼写纠错")
    print("  🌐 中英文双语")
    print("  👤 用户管理系统")
    print("  📊 搜索统计")
    print("  💾 结果导出")
    print("=" * 60)
    print("访问 http://localhost:5003 开始使用")
    
    # 预初始化搜索引擎
    init_search_engine()
    
    # 创建默认管理员用户
    if not user_manager.authenticate_user("admin", "admin123"):
        user_manager.create_user("admin", "admin123", "<EMAIL>")
        logger.info("已创建默认管理员用户: admin/admin123")
    
    app.run(
        host='0.0.0.0',
        port=5003,
        debug=False,
        threaded=True
    )
