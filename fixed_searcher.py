#!/usr/bin/env python3
"""
修复版数据字段检索器
解决相似度计算问题
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from difflib import SequenceMatcher
from field_searcher import SearchResult
import warnings
warnings.filterwarnings('ignore')

# 导入翻译和模糊搜索工具
try:
    from googletrans import Translator
    from deep_translator import GoogleTranslator
    TRANSLATION_AVAILABLE = True
except ImportError:
    TRANSLATION_AVAILABLE = False
    print("翻译工具未安装，将跳过翻译功能")

try:
    from fuzzywuzzy import fuzz, process
    from rapidfuzz import fuzz as rapid_fuzz, process as rapid_process
    FUZZY_SEARCH_AVAILABLE = True
except ImportError:
    FUZZY_SEARCH_AVAILABLE = False
    print("模糊搜索工具未安装，将使用基础搜索")

import json
import pickle
import time

class FixedFieldSearcher:
    """修复版字段搜索器"""
    
    def __init__(self, data_dir: str = "split_files", enable_translation: bool = False):
        self.data_dir = Path(data_dir)
        self.data = None
        self.tables = {}
        self.table_metadata = {}
        self.enable_translation = enable_translation and TRANSLATION_AVAILABLE
        self.chinese_translations = {}  # 存储中文翻译
        self.translation_cache_file = Path("cache/chinese_translations.json")

        # 初始化翻译器
        if self.enable_translation:
            try:
                self.translator = GoogleTranslator(source='en', target='zh-CN')
                print("✅ 翻译功能已启用")
            except Exception as e:
                print(f"⚠️ 翻译器初始化失败: {e}")
                self.enable_translation = False

        # 初始化jieba分词
        jieba.initialize()

        # 创建缓存目录
        Path("cache").mkdir(exist_ok=True)

        # 加载数据
        self._load_data()
        self._prepare_search_index()

        # 加载或创建中文翻译
        if self.enable_translation:
            self._load_or_create_translations()
    
    def _load_data(self):
        """加载所有CSV文件并保持表格分离"""
        print("正在加载数据文件...")
        
        self.tables = {}
        self.table_metadata = {}
        all_data = []
        csv_files = list(self.data_dir.glob("*.csv"))
        
        if not csv_files:
            raise FileNotFoundError(f"在目录 {self.data_dir} 中未找到CSV文件")
        
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                required_cols = ['id', 'description']
                if all(col in df.columns for col in required_cols):
                    table_name = file_path.stem
                    df['table_name'] = table_name
                    
                    self.tables[table_name] = df.copy()
                    self.table_metadata[table_name] = {
                        'file_path': str(file_path),
                        'record_count': len(df),
                        'regions': df['region'].unique().tolist() if 'region' in df.columns else [],
                        'types': df['type'].unique().tolist() if 'type' in df.columns else [],
                        'categories': df['category.name'].unique().tolist() if 'category.name' in df.columns else []
                    }
                    
                    all_data.append(df)
                    print(f"已加载: {file_path.name} ({len(df)} 条记录)")
                else:
                    print(f"跳过文件 {file_path.name}: 缺少必要列")
            except Exception as e:
                print(f"加载文件 {file_path.name} 时出错: {e}")
        
        if not all_data:
            raise ValueError("没有成功加载任何数据文件")
        
        self.data = pd.concat(all_data, ignore_index=True)
        self.data = self.data.drop_duplicates(subset=['id', 'description'])
        
        print(f"数据加载完成，共 {len(self.data)} 条唯一记录，来自 {len(self.tables)} 个表")
    
    def _prepare_search_index(self):
        """准备搜索索引"""
        print("正在构建搜索索引...")
        
        # 提取描述文本并清理
        self.descriptions = []
        for desc in self.data['description'].fillna('').astype(str):
            # 简单清理，保留原始文本的完整性
            cleaned_desc = re.sub(r'\s+', ' ', desc.strip())
            self.descriptions.append(cleaned_desc)
        
        # 构建简化的TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 2),
            min_df=2,  # 至少出现在2个文档中
            max_df=0.8,  # 最多出现在80%的文档中
            lowercase=True,
            stop_words='english'  # 使用英文停用词
        )
        
        # 计算TF-IDF矩阵
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(self.descriptions)
        
        print(f"搜索索引构建完成 - 词汇表大小: {len(self.tfidf_vectorizer.vocabulary_)}")

    def _load_or_create_translations(self):
        """加载或创建中文翻译"""
        print("正在处理中文翻译...")

        # 尝试加载现有翻译缓存
        if self.translation_cache_file.exists():
            try:
                with open(self.translation_cache_file, 'r', encoding='utf-8') as f:
                    self.chinese_translations = json.load(f)
                print(f"已加载 {len(self.chinese_translations)} 条翻译缓存")
            except Exception as e:
                print(f"加载翻译缓存失败: {e}")
                self.chinese_translations = {}

        # 检查是否需要翻译新的描述
        untranslated_descriptions = []
        for desc in self.descriptions:
            if desc and desc not in self.chinese_translations:
                untranslated_descriptions.append(desc)

        if untranslated_descriptions:
            print(f"发现 {len(untranslated_descriptions)} 条未翻译的描述，开始翻译...")
            self._translate_descriptions(untranslated_descriptions)
            self._save_translations()
        else:
            print("所有描述已翻译完成")

    def _translate_descriptions(self, descriptions: List[str], batch_size: int = 50):
        """批量翻译描述"""
        total = len(descriptions)
        translated_count = 0

        for i in range(0, total, batch_size):
            batch = descriptions[i:i + batch_size]

            for desc in batch:
                if not desc.strip():
                    continue

                try:
                    # 使用deep-translator进行翻译
                    chinese_text = self.translator.translate(desc)
                    self.chinese_translations[desc] = chinese_text
                    translated_count += 1

                    # 避免请求过于频繁
                    time.sleep(0.1)

                except Exception as e:
                    print(f"翻译失败 '{desc[:30]}...': {e}")
                    # 如果翻译失败，使用原文
                    self.chinese_translations[desc] = desc

            # 显示进度
            progress = min(i + batch_size, total)
            print(f"翻译进度: {progress}/{total} ({progress/total*100:.1f}%)")

            # 每批次后保存一次，防止数据丢失
            if i % (batch_size * 5) == 0:  # 每5个批次保存一次
                self._save_translations()

        print(f"翻译完成，成功翻译 {translated_count} 条描述")

    def _save_translations(self):
        """保存翻译到缓存文件"""
        try:
            with open(self.translation_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.chinese_translations, f, ensure_ascii=False, indent=2)
            print("翻译缓存已保存")
        except Exception as e:
            print(f"保存翻译缓存失败: {e}")

    def get_chinese_description(self, english_desc: str) -> str:
        """获取英文描述的中文翻译"""
        if not self.enable_translation:
            return english_desc
        return self.chinese_translations.get(english_desc, english_desc)
    
    def _expand_chinese_query(self, query: str) -> str:
        """扩展中文查询为英文同义词"""
        synonyms = {
            '价格': 'price cost value',
            '现金流': 'cash flow cashflow',
            '收入': 'revenue income earnings',
            '利润': 'profit earnings income',
            '资产': 'asset assets',
            '负债': 'liability debt',
            '股价': 'stock price share price',
            '市值': 'market cap capitalization mktcap',
            '成交量': 'volume trading volume',
            '波动': 'volatility variance',
            '风险': 'risk beta',
            '回报': 'return returns',
            '增长': 'growth',
            '比率': 'ratio rate',
            '指标': 'indicator metric',
            '财务': 'financial finance',
            '会计': 'accounting',
            '估值': 'valuation',
            '分析': 'analysis analytics',
            '收益': 'earnings yield return'
        }

        expanded_terms = []
        for chinese_term, english_terms in synonyms.items():
            if chinese_term in query:
                expanded_terms.append(english_terms)

        if expanded_terms:
            return query + ' ' + ' '.join(expanded_terms)
        return query

    def _calculate_id_priority_similarity(self, query: str) -> np.ndarray:
        """计算ID优先的相似度 - 平衡版本"""
        query_lower = query.lower()
        similarities = []

        for i, row in self.data.iterrows():
            field_id = str(row['id']).lower()
            desc = str(row['description']).lower()

            # 1. ID完全匹配 - 最高优先级，但不过分悬殊
            if query_lower == field_id:
                similarities.append(1.0)  # 标准化为1.0
                continue

            # 2. ID包含查询词 - 高优先级
            if query_lower in field_id:
                # 根据匹配程度计算分数
                match_ratio = len(query_lower) / len(field_id)
                similarities.append(0.7 + match_ratio * 0.25)  # 0.7-0.95分
                continue

            # 3. 查询词包含ID - 中高优先级
            if field_id in query_lower:
                id_ratio = len(field_id) / len(query_lower)
                similarities.append(0.6 + id_ratio * 0.2)  # 0.6-0.8分
                continue

            # 4. Description完全匹配 - 也给予较高分数
            if query_lower == desc:
                similarities.append(0.85)
                continue

            # 5. Description包含匹配
            if query_lower in desc:
                match_ratio = len(query_lower) / len(desc)
                similarities.append(0.5 + match_ratio * 0.3)  # 0.5-0.8分
                continue

            # 6. 关键词匹配
            query_words = set(query_lower.split())
            desc_words = set(desc.split())
            id_words = set(field_id.split('_'))  # ID通常用下划线分隔

            # 检查ID中的关键词匹配
            id_intersection = query_words.intersection(id_words)
            if id_intersection:
                match_score = len(id_intersection) / len(query_words)
                similarities.append(0.4 + match_score * 0.3)  # 0.4-0.7分
                continue

            # 检查描述中的关键词匹配
            desc_intersection = query_words.intersection(desc_words)
            if desc_intersection:
                jaccard_sim = len(desc_intersection) / len(query_words.union(desc_words))
                similarities.append(0.2 + jaccard_sim * 0.4)  # 0.2-0.6分
                continue

            # 7. 无匹配
            similarities.append(0.0)

        return np.array(similarities)

    def _calculate_fuzzy_search_similarity(self, query: str) -> np.ndarray:
        """使用开源模糊搜索工具计算相似度"""
        if not FUZZY_SEARCH_AVAILABLE:
            return self._calculate_simple_similarity(query)

        similarities = []
        query_lower = query.lower()

        # 准备搜索目标列表（ID + 描述 + 中文翻译）
        search_targets = []
        for i, row in self.data.iterrows():
            field_id = str(row['id']).lower()
            desc = str(row['description']).lower()

            # 添加ID、描述和中文翻译作为搜索目标
            targets = [field_id, desc]

            # 如果有中文翻译，也加入搜索
            if self.enable_translation:
                chinese_desc = self.get_chinese_description(row['description'])
                if chinese_desc != row['description']:
                    targets.append(chinese_desc.lower())

            search_targets.append(targets)

        # 使用rapidfuzz进行模糊匹配
        for targets in search_targets:
            max_similarity = 0.0

            for target in targets:
                if not target.strip():
                    continue

                # 使用多种模糊匹配算法
                ratio_score = rapid_fuzz.ratio(query_lower, target) / 100.0
                partial_score = rapid_fuzz.partial_ratio(query_lower, target) / 100.0
                token_sort_score = rapid_fuzz.token_sort_ratio(query_lower, target) / 100.0
                token_set_score = rapid_fuzz.token_set_ratio(query_lower, target) / 100.0

                # 取最高分
                similarity = max(ratio_score, partial_score, token_sort_score, token_set_score)
                max_similarity = max(max_similarity, similarity)

            similarities.append(max_similarity)

        return np.array(similarities)

    def _calculate_chinese_fuzzy_similarity(self, query: str) -> np.ndarray:
        """专门针对中文查询的模糊搜索"""
        if not self.enable_translation:
            return self._calculate_simple_similarity(query)

        similarities = []

        # 对于中文查询，主要在中文翻译中搜索
        for i, row in self.data.iterrows():
            chinese_desc = self.get_chinese_description(row['description'])
            field_id = str(row['id']).lower()
            english_desc = str(row['description']).lower()

            max_similarity = 0.0

            # 在中文翻译中搜索
            if chinese_desc and FUZZY_SEARCH_AVAILABLE:
                chinese_similarity = rapid_fuzz.ratio(query, chinese_desc) / 100.0
                max_similarity = max(max_similarity, chinese_similarity)

            # 也在英文ID和描述中搜索（处理英文词汇）
            if FUZZY_SEARCH_AVAILABLE:
                id_similarity = rapid_fuzz.partial_ratio(query.lower(), field_id) / 100.0
                desc_similarity = rapid_fuzz.partial_ratio(query.lower(), english_desc) / 100.0
                max_similarity = max(max_similarity, id_similarity, desc_similarity)

            # 使用扩展的中文查询
            expanded_query = self._expand_chinese_query(query)
            if expanded_query != query and FUZZY_SEARCH_AVAILABLE:
                expanded_similarity = rapid_fuzz.token_set_ratio(expanded_query.lower(), english_desc) / 100.0
                max_similarity = max(max_similarity, expanded_similarity)

            similarities.append(max_similarity)

        return np.array(similarities)

    def _is_chinese_query(self, query: str) -> bool:
        """检测查询是否包含中文字符"""
        for char in query:
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False

    def _calculate_simple_similarity(self, query: str) -> np.ndarray:
        """计算简单的文本相似度"""
        # 扩展中文查询
        expanded_query = self._expand_chinese_query(query)
        query_lower = expanded_query.lower()

        similarities = []

        for desc in self.descriptions:
            desc_lower = desc.lower()

            # 1. 完全匹配检查
            if query.lower() == desc_lower:
                similarities.append(1.0)
                continue

            # 2. 包含匹配检查
            if query.lower() in desc_lower:
                # 根据匹配长度计算相似度
                similarity = len(query.lower()) / len(desc_lower)
                similarities.append(min(similarity + 0.5, 1.0))
                continue

            # 3. 扩展查询匹配
            query_words = set(query_lower.split())
            desc_words = set(desc_lower.split())

            if query_words and desc_words:
                intersection = query_words.intersection(desc_words)
                union = query_words.union(desc_words)
                jaccard_sim = len(intersection) / len(union) if union else 0

                # 如果有关键词匹配，给予基础分数
                if intersection:
                    similarities.append(jaccard_sim * 0.8)
                else:
                    similarities.append(0.0)
            else:
                similarities.append(0.0)

        return np.array(similarities)
    
    def _calculate_enhanced_cosine_similarity(self, query: str) -> np.ndarray:
        """计算增强的余弦相似度"""
        try:
            # 预处理查询
            query_processed = re.sub(r'[^\w\s]', ' ', query.lower())
            query_processed = re.sub(r'\s+', ' ', query_processed.strip())
            
            # 计算查询向量
            query_vector = self.tfidf_vectorizer.transform([query_processed])
            
            # 计算余弦相似度
            similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
            
            return similarities
        except Exception as e:
            print(f"余弦相似度计算出错: {e}")
            return np.zeros(len(self.descriptions))
    
    def search(self, 
               query: str, 
               top_k: int = 10,
               similarity_method: str = 'enhanced',
               min_score: float = 0.01,
               region_filter: Optional[str] = None,
               type_filter: Optional[str] = None,
               table_filter: Optional[str] = None,
               category_filter: Optional[str] = None) -> List[SearchResult]:
        """
        执行搜索
        """
        if not query.strip():
            return []
        
        print(f"搜索查询: '{query}' (方法: {similarity_method})")
        
        # 计算相似度
        if similarity_method == 'simple':
            similarities = self._calculate_simple_similarity(query)
        elif similarity_method == 'cosine':
            similarities = self._calculate_enhanced_cosine_similarity(query)
        elif similarity_method == 'id_priority':
            similarities = self._calculate_id_priority_similarity(query)
        elif similarity_method == 'fuzzy_search':
            similarities = self._calculate_fuzzy_search_similarity(query)
        elif similarity_method == 'chinese_fuzzy':
            similarities = self._calculate_chinese_fuzzy_similarity(query)
        elif similarity_method == 'enhanced':
            # 组合方法：ID优先 + 简单匹配 + 余弦相似度
            id_sim = self._calculate_id_priority_similarity(query)
            simple_sim = self._calculate_simple_similarity(query)
            cosine_sim = self._calculate_enhanced_cosine_similarity(query)

            # 加权组合，优先考虑ID匹配
            similarities = 0.5 * id_sim + 0.3 * simple_sim + 0.2 * cosine_sim
        elif similarity_method == 'smart':
            # 智能方法：根据查询内容选择最佳算法
            if self._is_chinese_query(query):
                similarities = self._calculate_chinese_fuzzy_similarity(query)
            elif len(query.split()) == 1 and len(query) <= 10:
                # 短查询使用ID优先
                similarities = self._calculate_id_priority_similarity(query)
            else:
                # 长查询使用模糊搜索
                similarities = self._calculate_fuzzy_search_similarity(query)
        else:
            similarities = self._calculate_id_priority_similarity(query)  # 默认使用ID优先
        
        # 应用过滤器
        mask = similarities >= min_score
        if region_filter:
            mask &= (self.data['region'] == region_filter)
        if type_filter:
            mask &= (self.data['type'] == type_filter)
        if table_filter:
            mask &= (self.data['table_name'] == table_filter)
        if category_filter:
            mask &= (self.data['category.name'] == category_filter)
        
        # 获取有效索引
        valid_indices = np.where(mask)[0]
        valid_similarities = similarities[mask]
        
        if len(valid_similarities) == 0:
            print("找到 0 个匹配结果")
            return []
        
        # 排序并获取top_k结果
        sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
        
        # 构建结果
        results = []
        for idx in sorted_indices:
            original_idx = valid_indices[idx]
            row = self.data.iloc[original_idx]
            
            result = SearchResult(
                id=row['id'],
                description=row['description'],
                similarity_score=valid_similarities[idx],
                region=row.get('region', ''),
                universe=row.get('universe', ''),
                type=row.get('type', ''),
                category=row.get('category.name', ''),
                subcategory=row.get('subcategory.name', ''),
                dataset_name=row.get('dataset.name', ''),
                coverage=row.get('coverage', 0.0),
                user_count=row.get('userCount', 0),
                alpha_count=row.get('alphaCount', 0)
            )
            result.table_name = row.get('table_name', '')
            results.append(result)
        
        print(f"找到 {len(results)} 个匹配结果")
        return results
    
    def get_tables(self) -> Dict[str, Dict]:
        """获取所有表格信息"""
        return self.table_metadata
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        return sorted(self.data['category.name'].dropna().unique().tolist())
    
    def get_regions(self) -> List[str]:
        """获取所有地区"""
        return sorted(self.data['region'].dropna().unique().tolist())
    
    def get_types(self) -> List[str]:
        """获取所有类型"""
        return sorted(self.data['type'].dropna().unique().tolist())
    
    def get_statistics(self) -> Dict:
        """获取数据统计信息"""
        stats = {
            'total_records': len(self.data),
            'unique_regions': self.data['region'].nunique(),
            'unique_types': self.data['type'].nunique(),
            'unique_categories': self.data['category.name'].nunique(),
            'unique_tables': len(self.tables),
            'regions': self.data['region'].value_counts().to_dict(),
            'types': self.data['type'].value_counts().to_dict(),
            'categories': self.data['category.name'].value_counts().head(10).to_dict(),
            'tables': {name: meta['record_count'] for name, meta in self.table_metadata.items()}
        }
        return stats


def main():
    """测试修复版搜索器"""
    print("测试修复版数据字段检索器")
    print("="*50)
    
    try:
        searcher = FixedFieldSearcher()
        
        # 测试查询
        test_queries = [
            "market capitalization",
            "price",
            "volume",
            "cash flow",
            "earnings"
        ]
        
        for query in test_queries:
            print(f"\n测试查询: '{query}'")
            
            # 测试不同方法
            for method in ['simple', 'cosine', 'enhanced']:
                results = searcher.search(query, top_k=3, similarity_method=method, min_score=0.01)
                print(f"  {method} 方法: {len(results)} 个结果")
                
                if results:
                    best_result = results[0]
                    print(f"    最佳: [{best_result.similarity_score:.4f}] {best_result.id}: {best_result.description[:50]}...")
        
        print(f"\n修复版搜索器测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
