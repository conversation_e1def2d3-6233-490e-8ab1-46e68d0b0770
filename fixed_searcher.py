#!/usr/bin/env python3
"""
修复版数据字段检索器
解决相似度计算问题
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from difflib import SequenceMatcher
from field_searcher import SearchResult
import warnings
warnings.filterwarnings('ignore')

class FixedFieldSearcher:
    """修复版字段搜索器"""
    
    def __init__(self, data_dir: str = "split_files"):
        self.data_dir = Path(data_dir)
        self.data = None
        self.tables = {}
        self.table_metadata = {}
        
        # 初始化jieba分词
        jieba.initialize()
        
        # 加载数据
        self._load_data()
        self._prepare_search_index()
    
    def _load_data(self):
        """加载所有CSV文件并保持表格分离"""
        print("正在加载数据文件...")
        
        self.tables = {}
        self.table_metadata = {}
        all_data = []
        csv_files = list(self.data_dir.glob("*.csv"))
        
        if not csv_files:
            raise FileNotFoundError(f"在目录 {self.data_dir} 中未找到CSV文件")
        
        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                required_cols = ['id', 'description']
                if all(col in df.columns for col in required_cols):
                    table_name = file_path.stem
                    df['table_name'] = table_name
                    
                    self.tables[table_name] = df.copy()
                    self.table_metadata[table_name] = {
                        'file_path': str(file_path),
                        'record_count': len(df),
                        'regions': df['region'].unique().tolist() if 'region' in df.columns else [],
                        'types': df['type'].unique().tolist() if 'type' in df.columns else [],
                        'categories': df['category.name'].unique().tolist() if 'category.name' in df.columns else []
                    }
                    
                    all_data.append(df)
                    print(f"已加载: {file_path.name} ({len(df)} 条记录)")
                else:
                    print(f"跳过文件 {file_path.name}: 缺少必要列")
            except Exception as e:
                print(f"加载文件 {file_path.name} 时出错: {e}")
        
        if not all_data:
            raise ValueError("没有成功加载任何数据文件")
        
        self.data = pd.concat(all_data, ignore_index=True)
        self.data = self.data.drop_duplicates(subset=['id', 'description'])
        
        print(f"数据加载完成，共 {len(self.data)} 条唯一记录，来自 {len(self.tables)} 个表")
    
    def _prepare_search_index(self):
        """准备搜索索引"""
        print("正在构建搜索索引...")
        
        # 提取描述文本并清理
        self.descriptions = []
        for desc in self.data['description'].fillna('').astype(str):
            # 简单清理，保留原始文本的完整性
            cleaned_desc = re.sub(r'\s+', ' ', desc.strip())
            self.descriptions.append(cleaned_desc)
        
        # 构建简化的TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 2),
            min_df=2,  # 至少出现在2个文档中
            max_df=0.8,  # 最多出现在80%的文档中
            lowercase=True,
            stop_words='english'  # 使用英文停用词
        )
        
        # 计算TF-IDF矩阵
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(self.descriptions)
        
        print(f"搜索索引构建完成 - 词汇表大小: {len(self.tfidf_vectorizer.vocabulary_)}")
    
    def _calculate_simple_similarity(self, query: str) -> np.ndarray:
        """计算简单的文本相似度"""
        query_lower = query.lower()
        similarities = []
        
        for desc in self.descriptions:
            desc_lower = desc.lower()
            
            # 1. 完全匹配检查
            if query_lower == desc_lower:
                similarities.append(1.0)
                continue
            
            # 2. 包含匹配检查
            if query_lower in desc_lower:
                # 根据匹配长度计算相似度
                similarity = len(query_lower) / len(desc_lower)
                similarities.append(min(similarity + 0.5, 1.0))
                continue
            
            # 3. 关键词匹配
            query_words = set(query_lower.split())
            desc_words = set(desc_lower.split())
            
            if query_words and desc_words:
                intersection = query_words.intersection(desc_words)
                union = query_words.union(desc_words)
                jaccard_sim = len(intersection) / len(union) if union else 0
                
                # 如果有关键词匹配，给予基础分数
                if intersection:
                    similarities.append(jaccard_sim * 0.8)
                else:
                    similarities.append(0.0)
            else:
                similarities.append(0.0)
        
        return np.array(similarities)
    
    def _calculate_enhanced_cosine_similarity(self, query: str) -> np.ndarray:
        """计算增强的余弦相似度"""
        try:
            # 预处理查询
            query_processed = re.sub(r'[^\w\s]', ' ', query.lower())
            query_processed = re.sub(r'\s+', ' ', query_processed.strip())
            
            # 计算查询向量
            query_vector = self.tfidf_vectorizer.transform([query_processed])
            
            # 计算余弦相似度
            similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
            
            return similarities
        except Exception as e:
            print(f"余弦相似度计算出错: {e}")
            return np.zeros(len(self.descriptions))
    
    def search(self, 
               query: str, 
               top_k: int = 10,
               similarity_method: str = 'enhanced',
               min_score: float = 0.01,
               region_filter: Optional[str] = None,
               type_filter: Optional[str] = None,
               table_filter: Optional[str] = None,
               category_filter: Optional[str] = None) -> List[SearchResult]:
        """
        执行搜索
        """
        if not query.strip():
            return []
        
        print(f"搜索查询: '{query}' (方法: {similarity_method})")
        
        # 计算相似度
        if similarity_method == 'simple':
            similarities = self._calculate_simple_similarity(query)
        elif similarity_method == 'cosine':
            similarities = self._calculate_enhanced_cosine_similarity(query)
        elif similarity_method == 'enhanced':
            # 组合方法：简单匹配 + 余弦相似度
            simple_sim = self._calculate_simple_similarity(query)
            cosine_sim = self._calculate_enhanced_cosine_similarity(query)
            
            # 加权组合，优先考虑简单匹配
            similarities = 0.7 * simple_sim + 0.3 * cosine_sim
        else:
            similarities = self._calculate_simple_similarity(query)
        
        # 应用过滤器
        mask = similarities >= min_score
        if region_filter:
            mask &= (self.data['region'] == region_filter)
        if type_filter:
            mask &= (self.data['type'] == type_filter)
        if table_filter:
            mask &= (self.data['table_name'] == table_filter)
        if category_filter:
            mask &= (self.data['category.name'] == category_filter)
        
        # 获取有效索引
        valid_indices = np.where(mask)[0]
        valid_similarities = similarities[mask]
        
        if len(valid_similarities) == 0:
            print("找到 0 个匹配结果")
            return []
        
        # 排序并获取top_k结果
        sorted_indices = np.argsort(valid_similarities)[::-1][:top_k]
        
        # 构建结果
        results = []
        for idx in sorted_indices:
            original_idx = valid_indices[idx]
            row = self.data.iloc[original_idx]
            
            result = SearchResult(
                id=row['id'],
                description=row['description'],
                similarity_score=valid_similarities[idx],
                region=row.get('region', ''),
                universe=row.get('universe', ''),
                type=row.get('type', ''),
                category=row.get('category.name', ''),
                subcategory=row.get('subcategory.name', ''),
                dataset_name=row.get('dataset.name', ''),
                coverage=row.get('coverage', 0.0),
                user_count=row.get('userCount', 0),
                alpha_count=row.get('alphaCount', 0)
            )
            result.table_name = row.get('table_name', '')
            results.append(result)
        
        print(f"找到 {len(results)} 个匹配结果")
        return results
    
    def get_tables(self) -> Dict[str, Dict]:
        """获取所有表格信息"""
        return self.table_metadata
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        return sorted(self.data['category.name'].dropna().unique().tolist())
    
    def get_regions(self) -> List[str]:
        """获取所有地区"""
        return sorted(self.data['region'].dropna().unique().tolist())
    
    def get_types(self) -> List[str]:
        """获取所有类型"""
        return sorted(self.data['type'].dropna().unique().tolist())
    
    def get_statistics(self) -> Dict:
        """获取数据统计信息"""
        stats = {
            'total_records': len(self.data),
            'unique_regions': self.data['region'].nunique(),
            'unique_types': self.data['type'].nunique(),
            'unique_categories': self.data['category.name'].nunique(),
            'unique_tables': len(self.tables),
            'regions': self.data['region'].value_counts().to_dict(),
            'types': self.data['type'].value_counts().to_dict(),
            'categories': self.data['category.name'].value_counts().head(10).to_dict(),
            'tables': {name: meta['record_count'] for name, meta in self.table_metadata.items()}
        }
        return stats


def main():
    """测试修复版搜索器"""
    print("测试修复版数据字段检索器")
    print("="*50)
    
    try:
        searcher = FixedFieldSearcher()
        
        # 测试查询
        test_queries = [
            "market capitalization",
            "price",
            "volume",
            "cash flow",
            "earnings"
        ]
        
        for query in test_queries:
            print(f"\n测试查询: '{query}'")
            
            # 测试不同方法
            for method in ['simple', 'cosine', 'enhanced']:
                results = searcher.search(query, top_k=3, similarity_method=method, min_score=0.01)
                print(f"  {method} 方法: {len(results)} 个结果")
                
                if results:
                    best_result = results[0]
                    print(f"    最佳: [{best_result.similarity_score:.4f}] {best_result.id}: {best_result.description[:50]}...")
        
        print(f"\n修复版搜索器测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
